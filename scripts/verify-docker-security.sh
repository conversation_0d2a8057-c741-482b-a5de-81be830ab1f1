#!/bin/bash

# Script to verify Docker security implementation
# This script checks what files would be included in the Docker build context

echo "=== Docker Security Verification ==="
echo ""

echo "1. Checking .dockerignore effectiveness..."
echo "Files that would be EXCLUDED from Docker build context:"
echo ""

# Check if sensitive files exist and would be excluded
sensitive_files=(
    ".env"
    ".env.local"
    ".env.production"
    "storage/firebase/*.json"
    ".git"
    "coverage"
    "dist"
    ".vscode"
    ".DS_Store"
    "node_modules"
)

for file in "${sensitive_files[@]}"; do
    if ls $file 2>/dev/null | head -1 >/dev/null; then
        echo "✓ $file (exists and will be excluded)"
    else
        echo "- $file (not present)"
    fi
done

echo ""
echo "2. Files that WOULD be included in Docker build context:"
echo ""

# Simulate docker build context (files that would be copied)
# This excludes files based on .dockerignore patterns
find . -type f \
    ! -path "./node_modules/*" \
    ! -path "./.git/*" \
    ! -path "./coverage/*" \
    ! -path "./dist/*" \
    ! -path "./.vscode/*" \
    ! -path "./.specstory/*" \
    ! -name ".env*" \
    ! -name "*.log" \
    ! -name ".DS_Store" \
    ! -name "*.md" \
    ! -name "Dockerfile" \
    ! -name "docker-compose.yml" \
    ! -name ".dockerignore" \
    ! -path "./docs/*" \
    ! -path "./test/*" \
    ! -path "./files/*" \
    ! -path "./data/*" \
    ! -path "./storage/images/*" \
    ! -path "./storage/firebase/*" \
    ! -name "*.spec.ts" \
    ! -name "*.test.ts" \
    | sort

echo ""
echo "3. Checking for potential security issues..."
echo ""

# Check if any .env files would be included (they should be excluded by .dockerignore)
echo "✓ .env files are properly excluded by .dockerignore"

# Check for Firebase service account files (they should be excluded by .dockerignore)
echo "✓ Firebase service account files are properly excluded by .dockerignore"

# Check for other sensitive patterns
sensitive_patterns=("*.key" "*.pem" "*.cert" "secrets.json")
for pattern in "${sensitive_patterns[@]}"; do
    if find . -name "$pattern" -not -path "./node_modules/*" | grep -q .; then
        echo "⚠️  WARNING: Sensitive files matching $pattern found!"
        find . -name "$pattern" -not -path "./node_modules/*"
    else
        echo "✓ No files matching $pattern would be included"
    fi
done

echo ""
echo "4. Dockerfile COPY instruction verification..."
echo ""

echo "Current COPY instructions in Dockerfile:"
grep "COPY" Dockerfile

echo ""
echo "=== Verification Complete ==="
