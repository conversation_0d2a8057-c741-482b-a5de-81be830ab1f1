#!/usr/bin/env node

/**
 * Firebase Configuration Test Script
 * 
 * This script tests the Firebase configuration to ensure the environment variables
 * are properly set and Firebase can be initialized successfully.
 * 
 * Usage: node scripts/test-firebase-config.js
 */

const admin = require('firebase-admin');

// Load environment variables
require('dotenv').config();

function testFirebaseConfig() {
  console.log('🔥 Testing Firebase Configuration...\n');

  // Check for required environment variables
  console.log('📋 Checking Firebase configuration:');

  if (!process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
    console.log('❌ FIREBASE_SERVICE_ACCOUNT_JSON is required');
    console.log('\n📝 Please set FIREBASE_SERVICE_ACCOUNT_JSON with your Firebase service account JSON');
    console.log('   Example: FIREBASE_SERVICE_ACCOUNT_JSON=\'{"type":"service_account",...}\'');
    console.log('   Or use: node scripts/convert-firebase-json.js path/to/service-account.json');
    process.exit(1);
  }
  console.log('✅ FIREBASE_SERVICE_ACCOUNT_JSON is set');

  if (!process.env.FIREBASE_DATABASE_URL) {
    console.log('❌ FIREBASE_DATABASE_URL is required');
    console.log('   Example: FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/');
    process.exit(1);
  }
  console.log('✅ FIREBASE_DATABASE_URL is set');

  // Parse and validate service account JSON
  let serviceAccount;
  try {
    const serviceAccountJson = process.env.FIREBASE_SERVICE_ACCOUNT_JSON;
    const jsonString = serviceAccountJson.startsWith('{')
      ? serviceAccountJson
      : Buffer.from(serviceAccountJson, 'base64').toString('utf8');

    serviceAccount = JSON.parse(jsonString);
    console.log('✅ Successfully parsed Firebase service account JSON');

    // Validate required fields
    const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
    const missingFields = requiredFields.filter(field => !serviceAccount[field]);

    if (missingFields.length > 0) {
      console.log(`❌ Missing required fields in service account JSON: ${missingFields.join(', ')}`);
      process.exit(1);
    }
    console.log('✅ All required service account fields are present');

  } catch (error) {
    console.log('❌ Failed to parse FIREBASE_SERVICE_ACCOUNT_JSON:', error.message);
    console.log('\n💡 Tips:');
    console.log('   - Ensure the JSON is valid');
    console.log('   - For base64 encoded JSON, make sure it\'s properly encoded');
    console.log('   - Use single quotes around the JSON string in .env files');
    process.exit(1);
  }

  console.log('\n🔧 Testing Firebase Admin SDK initialization...');

  try {
    // Initialize Firebase Admin SDK
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
      });
    }

    console.log('✅ Firebase Admin SDK initialized successfully!');
    console.log(`📊 Project ID: ${serviceAccount.project_id}`);
    console.log(`🔗 Database URL: ${process.env.FIREBASE_DATABASE_URL}`);
    console.log(`📧 Service Account Email: ${serviceAccount.client_email}`);

    // Test Firebase services
    console.log('\n🧪 Testing Firebase services...');

    // Test Messaging service (for push notifications)
    admin.messaging();
    console.log('✅ Firebase Messaging service accessible');

    // Test Database service
    admin.database();
    console.log('✅ Firebase Realtime Database service accessible');

    console.log('\n🎉 All Firebase configuration tests passed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Remove the old JSON service account files from storage/firebase/');
    console.log('   2. Test push notifications in your application');
    console.log('   3. Verify database operations work correctly');
    console.log('   4. Revoke the old service account key in Firebase Console');

  } catch (error) {
    console.log('❌ Firebase configuration test failed:');
    console.error(error.message);

    if (error.message.includes('private_key')) {
      console.log('\n💡 Tips:');
      console.log('   - Copy the entire JSON content from Firebase Console as-is');
      console.log('   - No need to escape newlines - they\'re handled automatically');
      console.log('   - Use single quotes around the JSON string in .env files');
    }

    process.exit(1);
  }
}

// Run the test
testFirebaseConfig();
