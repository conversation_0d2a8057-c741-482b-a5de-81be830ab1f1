import * as mongoose from 'mongoose';
import { mongoDbUri } from '../src/app.config';

async function verifyUsers() {
  try {
    await mongoose.connect(mongoDbUri);
    console.log('✅ Connected to database');

    const db = mongoose.connection.db;
    const users = await db
      .collection('users')
      .find({})
      .toArray();

    console.log(`\n📊 Found ${users.length} users in database:\n`);

    users.forEach(user => {
      console.log(`👤 Username: ${user.username}`);
      console.log(`   Email: ${user.email || 'N/A'}`);
      console.log(`   FullName: ${user.fullName || 'N/A'}`);
      console.log(`   FirstName: ${user.firstName || 'N/A'}`);
      console.log(`   LastName: ${user.lastName || 'N/A'}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Has Password: ${!!user.password}`);
      console.log(`   IBD Subtype: ${user.ibdSubtype || 'N/A'}`);
      console.log(`   Fields: ${Object.keys(user).join(', ')}`);
      console.log('---');
    });
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
}

verifyUsers()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
