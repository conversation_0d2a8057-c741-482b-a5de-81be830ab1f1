#!/usr/bin/env node

/**
 * Firebase JSON to Environment Variable Converter
 * 
 * This script helps convert Firebase service account JSON files to environment variables
 * for secure deployment.
 * 
 * Usage: 
 *   node scripts/convert-firebase-json.js path/to/service-account.json
 *   node scripts/convert-firebase-json.js --base64 path/to/service-account.json
 */

const fs = require('fs');
const path = require('path');

function convertFirebaseJson(filePath, useBase64 = false) {
  console.log('🔥 Firebase JSON to Environment Variable Converter\n');

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.error(`❌ File not found: ${filePath}`);
    process.exit(1);
  }

  try {
    // Read and parse JSON file
    const jsonContent = fs.readFileSync(filePath, 'utf8');
    const serviceAccount = JSON.parse(jsonContent);

    console.log(`✅ Successfully parsed: ${path.basename(filePath)}`);
    console.log(`📊 Project ID: ${serviceAccount.project_id}`);
    console.log(`📧 Service Account: ${serviceAccount.client_email}\n`);

    if (useBase64) {
      // Option 1: Base64 encoded JSON
      const base64Json = Buffer.from(jsonContent).toString('base64');
      console.log('📋 Base64 Encoded Environment Variables:');
      console.log('=' .repeat(50));
      console.log(`FIREBASE_SERVICE_ACCOUNT_JSON=${base64Json}`);
      console.log(`FIREBASE_DATABASE_URL=https://${serviceAccount.project_id}-default-rtdb.firebaseio.com/`);
    } else {
      // Option 2: Direct JSON string
      console.log('📋 Direct JSON Environment Variables:');
      console.log('=' .repeat(50));
      console.log(`FIREBASE_SERVICE_ACCOUNT_JSON='${jsonContent.replace(/\n/g, '').replace(/\s+/g, ' ')}'`);
      console.log(`FIREBASE_DATABASE_URL=https://${serviceAccount.project_id}-default-rtdb.firebaseio.com/`);
    }

    console.log('\n💡 Usage Instructions:');
    console.log('1. Copy the environment variables above to your .env file');
    console.log('2. Update FIREBASE_DATABASE_URL if your database URL is different');
    console.log('3. Test the configuration using: node scripts/test-firebase-config.js');
    console.log('4. Remove the original JSON file after testing');

    console.log('\n🔒 Security Reminders:');
    console.log('- Never commit .env files to version control');
    console.log('- Use secure secret management in production');
    console.log('- Rotate service account keys regularly');
    console.log('- Revoke the old key after migration');

  } catch (error) {
    console.error('❌ Failed to process JSON file:', error.message);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const useBase64 = args.includes('--base64');
const filePath = args.find(arg => !arg.startsWith('--'));

if (!filePath) {
  console.log('Usage: node scripts/convert-firebase-json.js [--base64] <path-to-json-file>');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/convert-firebase-json.js storage/firebase/service-account.json');
  console.log('  node scripts/convert-firebase-json.js --base64 storage/firebase/service-account.json');
  process.exit(1);
}

convertFirebaseJson(filePath, useBase64);
