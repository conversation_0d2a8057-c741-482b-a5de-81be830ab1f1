# Production Environment Configuration for IBD-PRAM Backend
# Copy this file to .env.production and update with your production Firebase project details

# Database Configuration
DB_CONN=*******************************************:port/db?authSource=admin

# Application Configuration
BASE_URL=https://api.ibd-pram.com
PORT=3003
NODE_ENV=production
API_VERSION=v1

# CORS Configuration
ALLOWED_ORIGINS=https://app.ibd-pram.com,https://admin.ibd-pram.com

# JWT & Security Configuration
JWT_SECRET=production-secret-key-change-this
JWT_REFRESH_SECRET=production-refresh-secret-key-change-this
SESSION_SECRET=production-session-secret-key-change-this

# Token Expiration (in seconds)
TOKEN_EXPIRED=36500
REFRESH_TOKEN_EXPIRED=36500
SET_PASSWORD_TOKEN_EXPIRED=14400

# Application URLs
CLINICIAN_PORTAL_URL=https://admin.ibd-pram.com
URL_RESET_PASSWORD=https://admin.ibd-pram.com/account/token-verification-before-set-or-reset-password

# Firebase Configuration - PRODUCTION
# Copy the entire JSON content from Firebase Console service account key
FIREBASE_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"ibd-pram-production","private_key_id":"your-prod-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PROD_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-prod-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40ibd-pram-production.iam.gserviceaccount.com"}'
FIREBASE_DATABASE_URL=https://ibd-pram-production-default-rtdb.firebaseio.com/

# Email Configuration - SMTP (Production)
SMTP_EMAIL=*******
SMTP_URL=smtp.your-provider.com
SMTP_PORT=465
SMTP_USER=*******
SMTP_PASS=production-email-password

# Deep Links
DEEP_LINK_APP_STORE=https://apps.apple.com/app/ibd-pram/id123456789
DEEP_LINK_GOOGLE_STORE=https://play.google.com/store/apps/details?id=com.yourcompany.ibd_pram

# Security Configuration
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=15
PASSWORD_MIN_LENGTH=8
GLOBAL_RATE_LIMIT=100
AUTH_RATE_LIMIT=5
