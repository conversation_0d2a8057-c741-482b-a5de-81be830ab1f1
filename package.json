{"name": "b13-nestjs", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\"", "start": "node -r dotenv/config -r ts-node/register -r tsconfig-paths/register src/main.ts", "start:dev": "nodemon --config nodemon-debug.json", "start:debug": "nodemon --config nodemon-debug.json", "prestart:prod": "rm -rf dist && tsc", "start:prod": "node -r ./tsconfig-paths-bootstrap.js dist/src/main.js", "start:container": "node -r dotenv/config -r ts-node/register -r tsconfig-paths/register src/main.ts", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:security": "jest --testPathPattern=security", "security:monitor": "ts-node -r tsconfig-paths/register scripts/security-monitor.ts", "security:report": "ts-node -r tsconfig-paths/register scripts/security-monitor.ts report", "security:metrics": "ts-node -r tsconfig-paths/register scripts/security-monitor.ts metrics", "security:check": "ts-node -r tsconfig-paths/register scripts/security-monitor.ts check", "security:validate": "npm run security:check && npm run test:security", "cli": "ts-node -r dotenv/config -r tsconfig-paths/register src/cli.ts", "cli:prod": "node -r dotenv/config -r ./tsconfig-paths-bootstrap.js dist/src/cli.js", "verify:users": "ts-node -r dotenv/config -r tsconfig-paths/register scripts/verify-users.ts"}, "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@sendgrid/client": "^8.1.5", "@sendgrid/helpers": "^8.0.0", "@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "bluebird": "^3.7.2", "cache-manager": "^7.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "config": "^3.3.1", "csv-parser": "^2.3.3", "csv-writer": "^1.6.0", "dayjs": "^1.11.2", "excel4node": "^1.7.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.0.0", "express-session": "^1.17.1", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "helmet": "^7.0.0", "moment": "^2.30.1", "mongoose": "^7.6.0", "mongoose-paginate-v2": "^1.3.9", "nest-commander": "^3.17.0", "nodemailer": "^6.7.2", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "randexp": "^0.5.3", "reflect-metadata": "^0.1.12", "rimraf": "^4.3.1", "rxjs": "^7.8.2", "swagger-ui-express": "^4.3.0"}, "devDependencies": {"@nestjs/testing": "^11.1.3", "@swc/core": "^1.12.7", "@swc/jest": "^0.2.38", "@types/cache-manager": "^3.4.0", "@types/express": "^4.17.7", "@types/express-session": "^1.17.3", "@types/inquirer": "^8.2.11", "@types/jest": "^30.0.0", "@types/node": "^20.19.2", "@types/supertest": "^2.0.10", "@types/through": "^0.0.33", "concurrently": "^9.2.0", "dotenv": "^17.2.0", "jest": "^30.0.3", "nodemon": "^3.1.10", "prettier": "^3.6.2", "sonarqube-scanner": "^4.0.1", "supertest": "^3.4.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "3.8.0", "tslint": "5.16.0", "typescript": "^5.8.3", "wait-on": "^8.0.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "overrides": {"axios": "^1.8.3", "ajv": "^8.17.1"}, "resolutions": {"axios": "^1.8.3"}}