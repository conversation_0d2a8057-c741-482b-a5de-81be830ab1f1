{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es6", "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "incremental": true, "paths": {"@aos-schema/*": ["./database/schema/*"], "@aos-enum/*": ["./shared/enums/*"], "@aos-shared/*": ["./shared/*"], "@aos-validator/*": ["./shared/validators/*"], "@aos-decorator/*": ["./shared/decorators/*"], "@aos-interceptor/*": ["./shared/interceptors/*"], "@aos-pipe/*": ["./shared/pipes/*"], "@aos-guard/*": ["./shared/guards/*"], "@aos-extension/*": ["./shared/extensions/*"], "@aos-database/*": ["./database/*"], "@aos-features/*": ["./features/*"]}}, "exclude": ["node_modules"]}