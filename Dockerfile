# Using Node 20 LTS – Node 22 marks `req.query` as a read-only getter which
# breaks `express-mongo-sanitize` (it rewrites `req.query`). Switch back to
# the current LTS line until the library is updated or replaced.
FROM node:20.14.0-slim

# Create app directory
RUN mkdir -p /home/<USER>/aos_backend
WORKDIR /home/<USER>/aos_backend

# Copy package files first for better layer caching
COPY package*.json ./

RUN npm ci --ignore-scripts

# Copy application source code and configuration files
COPY config/ ./config/
COPY data/ ./data/
COPY scripts/ ./scripts/
COPY storage/ ./storage/
COPY src/ ./src/
COPY test/ ./test/
COPY tsconfig*.json ./
COPY nest-cli.json ./
COPY tsconfig-paths-bootstrap.js ./

# Change ownership to node user
RUN chown -R node:node /home/<USER>/aos_backend

# Switch to node user
USER node

# Note: .env files are intentionally excluded for security
# Environment variables should be provided at runtime via docker run -e or docker-compose

# Expose port
EXPOSE 3000

# Start the application in container mode with environment variable loading
CMD ["npm", "run", "start:container"]
