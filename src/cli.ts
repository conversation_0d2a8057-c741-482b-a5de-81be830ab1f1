import { Modu<PERSON> } from '@nestjs/common';
import { CommandFactory } from 'nest-commander';
import { AppModule } from './app.module';
import { CommandModule } from './commands/command.module';

@Module({
  imports: [AppModule, CommandModule],
})
class CliModule { }

async function bootstrap() {
  try {
    await CommandFactory.run(CliModule, ['warn', 'error']);
    console.log('✅ Command completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

bootstrap();
  