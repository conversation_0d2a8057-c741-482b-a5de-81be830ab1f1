import { Module } from '@nestjs/common';
import { LoggerModule } from './../logger/logger.module';
import { MailerService } from './../shared/services/mailer.service';
import { databaseProviders } from './database.provider';
import {
  appConfigProviders,
  modelProviders,
  patientResourceProviders,
  questionProviders,
  testKccqHistoryProviders,
  testProviders,
  testResultProviders,
  testTimedUpHistoryProviders,
} from './model.providers';

@Module({
  imports: [LoggerModule.forRoot()],
  providers: [
    ...databaseProviders,
    ...modelProviders,
    ...patientResourceProviders,
    ...testProviders,
    ...questionProviders,
    ...testResultProviders,
    ...testTimedUpHistoryProviders,
    ...testKccqHistoryProviders,
    ...appConfigProviders,
    MailerService,
  ],
  exports: [
    ...databaseProviders,
    ...modelProviders,
    ...patientResourceProviders,
    ...testProviders,
    ...questionProviders,
    ...testResultProviders,
    ...testTimedUpHistoryProviders,
    ...testKccqHistoryProviders,
    ...appConfigProviders,
  ],
})
export class DatabaseModule {}
