import { Connection } from 'mongoose';
import { TestResultStatus } from './../shared/enums/test-result-status.enum';
import { TestType } from './../shared/enums/test-type.enum';
import { MailerService } from './../shared/services/mailer.service';
import { AosCollections, DatabaseConstants, getCollectionName, getCollectionToken } from './database.constant';
import { AppConfigSchema } from './schema/app-config.schema';
import { PatientResourceSchema } from './schema/patient-resource.schema';
import { QuestionSchema } from './schema/question.schema';
import { TestKccqHistorySchema } from './schema/test-kccq-history.schema';
import { TestResultSchema } from './schema/test-result.schema';
import { TestTimedUpHistorySchema } from './schema/test-timed-up-history.schema';
import { TestSchema } from './schema/test.schema';
import { UserSchema } from './schema/user.schema';

export const modelProviders = [
  {
    provide: getCollectionToken(AosCollections.User),
    useFactory: (connection: Connection) => connection.model(getCollectionName(AosCollections.User), UserSchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const patientResourceProviders = [
  {
    provide: getCollectionToken(AosCollections.PatientResource),
    useFactory: (connection: Connection) =>
      connection.model(getCollectionName(AosCollections.PatientResource), PatientResourceSchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const testProviders = [
  {
    provide: getCollectionToken(AosCollections.Test),
    useFactory: (connection: Connection) => connection.model(getCollectionName(AosCollections.Test), TestSchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const questionProviders = [
  {
    provide: getCollectionToken(AosCollections.Question),
    useFactory: (connection: Connection) =>
      connection.model(getCollectionName(AosCollections.Question), QuestionSchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const testTimedUpHistoryProviders = [
  {
    provide: getCollectionToken(AosCollections.TestTimedUpHistory),
    useFactory: (connection: Connection) =>
      connection.model(getCollectionName(AosCollections.TestTimedUpHistory), TestTimedUpHistorySchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const testKccqHistoryProviders = [
  {
    provide: getCollectionToken(AosCollections.TestKccqHistory),
    useFactory: (connection: Connection) =>
      connection.model(getCollectionName(AosCollections.TestKccqHistory), TestKccqHistorySchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const appConfigProviders = [
  {
    provide: getCollectionToken(AosCollections.AppConfig),
    useFactory: (connection: Connection) =>
      connection.model(getCollectionName(AosCollections.AppConfig), AppConfigSchema),
    inject: [DatabaseConstants.dbToken],
  },
];

export const TestResultSchemaFactory = (connection: Connection, mailerService: MailerService) => {
  const autoPopulate = function (next) {
    this.populate('patient');
    next();
  };

  TestResultSchema.pre('findOne', autoPopulate);
  TestResultSchema.pre('find', autoPopulate);

  TestResultSchema.pre('save', function (next) {
    const item = this;

    if (!item.isModified('redFlag')) {
      return next();
    }

    if (item.status === TestResultStatus.CANCELLED && item.type !== TestType.SYMPTOMS) {
      item.redFlag = false;
    }

    // NOTE: Email notifications are now handled by FlagLoggingService
    // This hook only manages the redFlag field state
    // The new RedFlagEmailService provides enhanced email templates and deep-link functionality

    return next();
  });

  return connection.model(getCollectionName(AosCollections.TestResult), TestResultSchema);
};

export const testResultProviders = [
  {
    provide: getCollectionToken(AosCollections.TestResult),
    useFactory: TestResultSchemaFactory,
    inject: [DatabaseConstants.dbToken, MailerService],
  },
];
