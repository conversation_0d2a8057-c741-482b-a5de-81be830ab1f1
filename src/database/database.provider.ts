import * as mongoose from 'mongoose';
import { isDevMode, mongoDbUri } from '../app.config';
import { DatabaseConstants } from './database.constant';

export const databaseProviders = [
  {
    provide: DatabaseConstants.dbToken,
    useFactory: async (): Promise<typeof mongoose> => {
      // Remove deprecated settings for Mongoose 8.x
      mongoose.set('debug', isDevMode);
      return await mongoose.connect(mongoDbUri);
    },
  },
];
