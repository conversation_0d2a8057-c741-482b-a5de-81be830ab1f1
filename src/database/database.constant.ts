export class DatabaseConstants {
  static readonly dbToken = 'DBToken';
}

export function getCollectionToken(collection: AosCollections) {
  switch (collection) {
    case AosCollections.User:
      return 'UserToken';
    case AosCollections.PatientResource:
      return 'PatientResourceToken';
    case AosCollections.Test:
      return 'TestToken';
    case AosCollections.Question:
      return 'QuestionToken';
    case AosCollections.TestResult:
      return 'TestResultToken';
    case AosCollections.TestTimedUpHistory:
      return 'TestTimedUpHistoryToken';
    case AosCollections.TestKccqHistory:
      return 'TestKccqHistoryToken';
    case AosCollections.AppConfig:
      return 'AppConfigToken';
  }
}

export function getCollectionName(collection: AosCollections) {
  switch (collection) {
    case AosCollections.User:
      return 'User';
    case AosCollections.PatientResource:
      return 'PatientResource';
    case AosCollections.Test:
      return 'Test';
    case AosCollections.Question:
      return 'Question';
    case AosCollections.TestResult:
      return 'TestResult';
    case AosCollections.TestTimedUpHistory:
      return 'TestTimedUpHistory';
    case AosCollections.TestKccqHistory:
      return 'TestKccqHistory';
    case AosCollections.AppConfig:
      return 'AppConfig';
  }
}

export enum AosCollections {
  User,
  PatientResource,
  Test,
  Question,
  TestResult,
  TestTimedUpHistory,
  TestKccqHistory,
  AppConfig,
}

export enum AppConfigType {
  RED_FLAG_EMAIL = 'RED_FLAG_EMAIL',
}
