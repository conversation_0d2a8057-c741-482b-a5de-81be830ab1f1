import { Connection } from 'mongoose';
import { AppConfigType } from '../database.constant';

/**
 * Database migration to seed initial RED_FLAG_EMAIL configuration
 * 
 * This migration creates the initial configuration entry for the red flag email system.
 * It sets a default email address that can be updated later through the admin interface.
 * 
 * Usage:
 * 1. Import this function in your migration runner
 * 2. Call seedRedFlagEmailConfig(connection) with your MongoDB connection
 * 3. The migration will create the initial configuration if it doesn't exist
 * 
 * @param connection - MongoDB connection instance
 */
export async function seedRedFlagEmailConfig(connection: Connection): Promise<void> {
  try {
    const appConfigCollection = connection.collection('appconfigs');
    
    // Check if RED_FLAG_EMAIL configuration already exists
    const existingConfig = await appConfigCollection.findOne({ 
      key: AppConfigType.RED_FLAG_EMAIL 
    });
    
    if (existingConfig) {
      console.log('RED_FLAG_EMAIL configuration already exists, skipping migration');
      return;
    }
    
    // Create initial RED_FLAG_EMAIL configuration
    const initialConfig = {
      key: AppConfigType.RED_FLAG_EMAIL,
      value: '<EMAIL>', // Default email - should be updated via admin interface
      description: 'Email address for receiving red flag alert notifications',
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: 'system-migration',
    };
    
    await appConfigCollection.insertOne(initialConfig);
    
    console.log('Successfully seeded RED_FLAG_EMAIL configuration with default value');
    console.log('Please update the email address through the admin interface at /admin/config/red-flag-email');
  } catch (error) {
    console.error('Failed to seed RED_FLAG_EMAIL configuration:', error.message);
    throw error;
  }
}

/**
 * Rollback function to remove the RED_FLAG_EMAIL configuration
 * 
 * @param connection - MongoDB connection instance
 */
export async function rollbackRedFlagEmailConfig(connection: Connection): Promise<void> {
  try {
    const appConfigCollection = connection.collection('appconfigs');
    
    const result = await appConfigCollection.deleteOne({ 
      key: AppConfigType.RED_FLAG_EMAIL 
    });
    
    if (result.deletedCount > 0) {
      console.log('Successfully removed RED_FLAG_EMAIL configuration');
    } else {
      console.log('RED_FLAG_EMAIL configuration not found, nothing to rollback');
    }
  } catch (error) {
    console.error('Failed to rollback RED_FLAG_EMAIL configuration:', error.message);
    throw error;
  }
}

/**
 * Migration instructions:
 * 
 * 1. To run this migration:
 *    ```typescript
 *    import { seedRedFlagEmailConfig } from './src/database/migrations/seed-red-flag-email-config';
 *    import { connection } from './src/database/database.providers';
 *    
 *    await seedRedFlagEmailConfig(connection);
 *    ```
 * 
 * 2. To rollback this migration:
 *    ```typescript
 *    import { rollbackRedFlagEmailConfig } from './src/database/migrations/seed-red-flag-email-config';
 *    import { connection } from './src/database/database.providers';
 *    
 *    await rollbackRedFlagEmailConfig(connection);
 *    ```
 * 
 * 3. After running the migration, update the email address through the admin API:
 *    PUT /admin/config/red-flag-email
 *    Body: { "email": "<EMAIL>" }
 *    Headers: Authorization: Bearer <super-admin-token>
 */
