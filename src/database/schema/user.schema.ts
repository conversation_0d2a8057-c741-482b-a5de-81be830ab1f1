import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { UserRole } from '@aos-enum/user-role.enum';
import * as bcrypt from 'bcrypt';
import { Schema } from 'mongoose';

export const UserSchema = new Schema({
  username: {
    type: String,
    unique: true,
  },
  email: {
    type: String,
  },
  password: {
    type: String,
  },
  fullName: {
    type: String,
  },
  firstName: {
    type: String,
  },
  lastName: {
    type: String,
  },
  nhsNumber: {
    type: String,
  },
  badge: {
    type: Number,
    default: 0,
  },
  deviceTokens: [
    {
      type: String,
    },
  ],
  designation: {
    type: String,
  },
  site: {
    type: String,
  },
  mobileNumber: {
    type: String,
  },
  isFirstTimeLogin: { type: Boolean, default: true },
  role: {
    type: Number,
    enum: [UserRole.CLINICIAN, UserRole.PATIENT, UserRole.SUPER_ADMIN, UserRole.DEMO],
    default: UserRole.PATIENT,
  },
  createdAt: { type: Date, default: Date.now },
  resetPasswordToken: {
    type: String,
  },
  archiveReason: {
    type: String,
  },
  requestResetPasswordDate: {
    type: Date,
  },
  isArchived: {
    type: Boolean,
    default: false,
  },
  dateOfBirth: {
    type: Date,
  },
  revealPassword: {
    type: String,
  },
  refreshToken: {
    type: String,
    default: '',
  },
  appDownloadToken: {
    type: String,
  },
  ibdSubtype: {
    type: String,
    enum: Object.values(IBDSubtype),
    required: false,
  },
  sessionIBDSubtype: {
    type: String,
    enum: Object.values(IBDSubtype),
    required: false,
  },
  expectedDueDate: {
    type: Date,
    required: false,
  },
  // New fields for IBD-PRAM patient onboarding
  age: {
    type: Number,
    required: false,
  },
  uuid: {
    type: String,
    maxlength: 50,
    required: false,
  },
  ethnicity: {
    type: String,
    enum: [
      'White',
      'Mixed/Multiple ethnic groups',
      'Asian/Asian British',
      'Black, Black British, Caribbean or African',
      'Other'
    ],
    required: false,
  },
  smokingStatus: {
    type: String,
    enum: [
      'None',
      'Rarely (monthly)',
      'Socially (weekly)',
      'Trying to quit',
      'Often (daily)',
      'Heavily (daily)'
    ],
    required: false,
  },
  yearOfDiagnosis: {
    type: Date,
    required: false,
  },
  parity: {
    type: String,
    enum: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '8+'],
    required: false,
  },
  currentMeds: {
    type: String,
    maxlength: 600,
    required: false,
  },
  lastDiseaseActivity: {
    type: Date,
    required: false,
  },
  previousMeds: {
    type: String,
    maxlength: 600,
    required: false,
  },
  medicalHistory: {
    type: String,
    maxlength: 600,
    required: false,
  },
  lastFcpDate: {
    type: Date,
    required: false,
  },
  lastFcpResult: {
    type: String,
    maxlength: 50,
    required: false,
  },
  emailRetryCount: {
    type: Number,
    default: 0,
  },
  lastEmailAttempt: {
    type: Date,
    required: false,
  },
});

UserSchema.index({
  firstName: 'text',
  lastName: 'text',
  nhsNumber: 'text',
});

UserSchema.pre('save', function (next) {
  const user = this;

  if (!user.isModified('password')) {
    return next();
  }

  bcrypt.genSalt(10, (err, salt) => {
    if (err) {
      return next(err);
    }

    bcrypt.hash(user.password, salt, (hErr, hash) => {
      if (hErr) {
        return next(hErr);
      }

      user.password = hash;
      next();
    });
  });
});

UserSchema.methods.checkPassword = function (attempt) {
  const user = this;
  return bcrypt.compare(attempt, user.password);
};
