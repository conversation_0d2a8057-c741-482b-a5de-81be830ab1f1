import { Schema } from 'mongoose';
const mongoosePaginate = require('mongoose-paginate-v2');

export const TestKccqHistorySchema = new Schema(
  {
    patient: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    testResult: {
      type: Schema.Types.ObjectId,
      ref: 'TestResult',
    },
    totalScores: { type: Number, default: 0 },
    delta: { type: Number, default: 0 },
    countValue: { type: Number, default: 0 },
    redFlag: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
  },
  { collection: 'test-kccq-history' },
);

TestKccqHistorySchema.plugin(mongoosePaginate);
