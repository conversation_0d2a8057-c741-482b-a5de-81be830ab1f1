import { Schema } from 'mongoose';
import { QuestionType } from './../../shared/enums/question-type.enum';
import { RAGType } from './../../shared/enums/rag-type.enum';

export const QuestionSchema = new Schema({
  text: {
    type: String,
  },
  code: { type: String },
  order: { type: Number },
  questions: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Question',
    },
  ],
  answers: [
    {
      text: String,
      code: String,
      selected: { type: Boolean, default: false },
      questions: [
        {
          type: Schema.Types.ObjectId,
          ref: 'Question',
        },
      ],
      rag: {
        type: String,
        enum: Object.values(RAGType),
      },
      score: Number,
      anxietyScore: Number,
      depressionScore: Number,
      sibdqScore: Number,
      eqScore: Number,
      additionalNote: String,
      point: Number,
    },
  ],
  type: {
    type: String,
    enum: Object.values(QuestionType),
    default: QuestionType.MULTIPLE_CHOICE_TYPE,
  },
  mandatory: {
    type: Boolean,
    default: true,
  },
  testDate: { type: String },
  textValue: { type: String },
  numericValue: { type: Number },
  createdAt: { type: Date, default: Date.now },
});

