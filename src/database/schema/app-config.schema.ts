import { Schema } from 'mongoose';

export const AppConfigSchema = new Schema({
  key: {
    type: String,
    unique: true,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
}, { collection: 'app-configs' });
