import { Schema } from 'mongoose';
import { IBDSubtype } from './../../shared/enums/ibd-subtype.enum';
import { TestType } from './../../shared/enums/test-type.enum';

export const TestSchema = new Schema({
  title: {
    type: String,
    unique: true,
  },
  disclaimer: {
    type: String,
  },
  instructions: [
    {
      title: String,
      image: String,
      content: String,
    },
  ],
  intervalDays: Number,
  firstTimeIntervalDays: Number,
  questions: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Question',
    },
  ],
  type: {
    type: Number,
    enum: [TestType.SYMPTOMS, TestType.MENTAL_WELLBEING, TestType.GENERAL_WELLBEING],
    default: TestType.SYMPTOMS,
  },
  ibdSubtype: {
    type: String,
    enum: Object.values(IBDSubtype),
    required: false,
  },
  createdAt: { type: Date, default: Date.now },
});
