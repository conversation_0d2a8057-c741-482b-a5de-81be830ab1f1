import { Schema } from 'mongoose';
const mongoosePaginate = require('mongoose-paginate-v2');

export const TestTimedUpHistorySchema = new Schema(
  {
    patient: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    testResult: {
      type: Schema.Types.ObjectId,
      ref: 'TestResult',
    },
    timeScore: { type: Number, default: 0 },
    delta: { type: Number, default: 0 },
    countValue: { type: Number, default: 0 },
    redFlag: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
  },
  { collection: 'test-timed-up-history' },
);

TestTimedUpHistorySchema.plugin(mongoosePaginate);
