import { TestResultStatus } from '@aos-enum/test-result-status.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { Schema } from 'mongoose';
const mongoosePaginate = require('mongoose-paginate-v2');

export const TestResultSchema = new Schema(
  {
    patient: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    acknowledgedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    test: {
      type: Schema.Types.ObjectId,
      ref: 'Test',
    },
    title: { type: String, default: 'Symptoms' },
    type: {
      type: Number,
      enum: [TestType.SYMPTOMS, TestType.MENTAL_WELLBEING, TestType.GENERAL_WELLBEING],
      default: TestType.SYMPTOMS,
    },
    detail: {
      type: String,
      get(data) {
        try {
          return JSON.parse(data.split('_id').join('id'));
        } catch (e) {
          return data;
        }
      },
      set(data) {
        return JSON.stringify(data);
      },
    },
    reds: { type: Number, default: 0 },
    greens: { type: Number, default: 0 },
    ambers: { type: Number, default: 0 },
    report: [
      {
        code: { type: String },
        question: { type: String },
        answer: { type: String },
        rag: { type: String },
        redFlag: { type: Boolean, default: false },
        score: { type: Number },
        order: { type: Number },
        numericValue: { type: Number },
        testDate: { type: Date },
        textValue: { type: String },
        // Mental Wellbeing scores (Test 2)
        anxietyScore: { type: Number, default: null },
        depressionScore: { type: Number, default: null },
        // General Wellbeing scores (Test 3)
        sibdqScore: { type: Number, default: null },
        eqScore: { type: Number, default: null },
      },
    ],
    physicalLimitationScore: { type: Number, default: null },
    symptomStabilityScore: { type: Number, default: null },
    symptomFrequencyScore: { type: Number, default: null },
    symptomBurdenScore: { type: Number, default: null },
    totalSymptomScore: { type: Number, default: null },
    selfEfficacyScore: { type: Number, default: null },
    qualityOfLifeScore: { type: Number, default: null },
    socialLimitationScore: { type: Number, default: null },
    totalScores: { type: Number, default: null },
    anxietyScore: { type: Number, default: null },
    depressionScore: { type: Number, default: null },
    clinicalSummaryScore: { type: Number, default: null },
    overDueCount: { type: Number, default: null },
    overDueTriggered: { type: Boolean, default: false },
    previousCompletedDate: { type: Date, default: null },
    ttc: { type: Number, default: 0 },
    notes: String,
    notesSaved: { type: Boolean, default: false },
    nhsNumber: String,
    redFlag: { type: Boolean, default: false },
    acknowledged: { type: Boolean, default: false },
    acknowledgedAt: { type: Date },
    status: {
      type: Number,
      enum: [TestResultStatus.NEW, TestResultStatus.COMPLETED, TestResultStatus.CANCELLED, TestResultStatus.OVERDUE],
      default: TestResultStatus.NEW,
    },
    startDate: { type: Date },
    completedDate: { type: Date },
    dueDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    sibdqScore: { type: Number, default: null },
    eqScore: { type: Number, default: null },
    flagDetails: [
      {
        flagType: { type: String }, // 'RED_AMBER_RESULT', 'SCORE_INCREASE', 'SUSTAINED_INCREASE', 'MENTAL_THRESHOLD', 'OVERDUE'
        flagReason: { type: String }, // Detailed reason for flag
        flaggedAt: { type: Date, default: Date.now },
        patientUsername: { type: String },
        testType: { type: Number },
        testSubtype: { type: String }, // 'HBI' or 'SCCAI' for symptoms
        scoreDifference: { type: Number }, // For score increase flags
        currentScore: { type: Number },
        previousScore: { type: Number },
        anxietyScore: { type: Number }, // For mental wellbeing flags
        depressionScore: { type: Number }, // For mental wellbeing flags
        daysSinceLastTest: { type: Number }, // For overdue flags
        questionNumbers: [{ type: String }], // For red/amber flags
      },
    ],
  },
  { collection: 'test-results' },
);

TestResultSchema.plugin(mongoosePaginate);
