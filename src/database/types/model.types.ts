import { Document, Model } from 'mongoose';

// User Document Interface
export interface IUserDocument extends Document {
  _id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  designation?: string;
  mobileNumber?: string;
  site?: string;
  nhsNumber?: string;
  role: number;
  isArchived: boolean;
  isActivated: boolean;
  resetPasswordToken?: string;
  resetPasswordTokenExpiredAt?: Date;
  requestResetPasswordDate?: string;
  archiveReason?: string;
  revealPassword?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Test Result Document Interface
export interface ITestResultDocument extends Document {
  _id: string;
  patient: string;
  testType: number;
  testSubType: number;
  status: number;
  dueDate: Date;
  submittedAt?: Date;
  questions: any[];
  score?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Test Document Interface
export interface ITestDocument extends Document {
  _id: string;
  type: number;
  subType: number;
  title: string;
  description: string;
  questions: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Question Document Interface
export interface IQuestionDocument extends Document {
  _id: string;
  code: string;
  text: string;
  type: number;
  answers: any[];
  isMandatory: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Patient Resource Document Interface
export interface IPatientResourceDocument extends Document {
  _id: string;
  title: string;
  description: string;
  url?: string;
  type: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Test History Document Interfaces
export interface ITestTimedUpHistoryDocument extends Document {
  _id: string;
  testResult: string;
  timeInSeconds: number;
  acknowledgedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITestKccqHistoryDocument extends Document {
  _id: string;
  testResult: string;
  score: number;
  answers: any[];
  createdAt: Date;
  updatedAt: Date;
}

// Model Types
export type IUserModel = Model<IUserDocument>;
export type ITestResultModel = Model<ITestResultDocument>;
export type ITestModel = Model<ITestDocument>;
export type IQuestionModel = Model<IQuestionDocument>;
export type IPatientResourceModel = Model<IPatientResourceDocument>;
export type ITestTimedUpHistoryModel = Model<ITestTimedUpHistoryDocument>;
export type ITestKccqHistoryModel = Model<ITestKccqHistoryDocument>;
