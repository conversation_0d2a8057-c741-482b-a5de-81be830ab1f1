import * as config from 'config';
import { NodeEnv } from './shared/enums/node-env.enum';

export const mongoDbUri = process.env.DB_CONN || config.get('Database.dbConfig.uri');
export const baseUrl = process.env.BASE_URL || config.get('Application.baseUrl');
export const jwtSecret: string = process.env.JWT_SECRET || config.get('Secret.jwtKey');
export const jwtRefreshSecret: string = process.env.JWT_REFRESH_SECRET || config.get('Secret.jwtRefreshKey');
export const sessionSecret: string = process.env.SESSION_SECRET || config.get('Secret.sessionKey');
export const appPort = process.env.PORT || config.get('Application.port');
export const tokenExpired = process.env.TOKEN_EXPIRED
  ? parseInt(process.env.TOKEN_EXPIRED)
  : config.get('Application.tokenExpired');
export const apiVersion = process.env.API_VERSION || config.get('Application.version');
export const clinicianPortalUrl = process.env.CLINICIAN_PORTAL_URL || config.get('Application.clinicianPortalUrl');
export const isDevMode = process.env.NODE_ENV === NodeEnv.DEVELOPMENT;
export const seeder = process.env.API_VERSION || config.get('Seeder');
export const timedUpPredefinedThreshold =
  process.env.TIMED_UP_PREDEFINED_THRESHOLD || config.get('Application.timedUpPredefinedThreshold');
export const healthStatusPredefinedThreshold =
  process.env.HEALTH_STATUS_PREDEFINED_THRESHOLD || config.get('Application.healthStatusPredefinedThreshold');
export const filePathReport = process.env.FILE_PATH_REPORT || config.get('Application.filePathReport');

export const refreshTokenExpired = process.env.REFRESH_TOKEN_EXPIRED
  ? parseInt(process.env.REFRESH_TOKEN_EXPIRED)
  : config.get('Application.refreshTokenExpired');

// Third party configurations
export const sendGridApiKey = process.env.SENDGRID_API_KEY || config.get('SendGrid.apiKey');
export const sendGridSender = process.env.SENDGRID_SENDER || config.get('SendGrid.sender');

// Firebase configuration - secure credential loading from FIREBASE_SERVICE_ACCOUNT_JSON
export const firebaseServiceAccount = (() => {
  if (!process.env.FIREBASE_SERVICE_ACCOUNT_JSON) {
    throw new Error(
      'FIREBASE_SERVICE_ACCOUNT_JSON environment variable is required. Please set it with your Firebase service account JSON.',
    );
  }

  try {
    const serviceAccountJson = process.env.FIREBASE_SERVICE_ACCOUNT_JSON;
    // Handle base64 encoded JSON (common in some deployment environments)
    const jsonString = serviceAccountJson.startsWith('{')
      ? serviceAccountJson
      : Buffer.from(serviceAccountJson, 'base64').toString('utf8');

    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Failed to parse FIREBASE_SERVICE_ACCOUNT_JSON:', error.message);
    throw new Error('Invalid FIREBASE_SERVICE_ACCOUNT_JSON format. Please ensure it contains valid JSON.');
  }
})();

export const firebaseDatabaseURL = process.env.FIREBASE_DATABASE_URL || config.get('Firebase.databaseURL');
export const setPasswordTokenExpired =
  process.env.SET_PASSWORD_TOKEN_EXPIRED || config.get('Application.setPasswordTokenExpired');

export const deleteExpiredTokenUserCron = config.get('Application.deleteExpiredTokenUserCron');

export const deepLinkAppStore = process.env.DEEP_LINK_APP_STORE || config.get('Application.deepLinkAppStore');

export const deepLinkGoogleStore = process.env.DEEP_LINK_GOOGLE_STORE || config.get('Application.deepLinkGoogleStore');

// Cronjob
export const cronTestOverDate = config.get('Cron.testOverDate');
export const cronTimezone = config.get('Cron.timezone') || 'Europe/London';

// SMTLP
export const smtpEmail = process.env.SMTP_EMAIL || config.get('Smtp.email');
export const smtpUrl = process.env.SMTP_URL || config.get('Smtp.url');
export const smtpPort = Number(process.env.SMTP_PORT) || Number(config.get('Smtp.port'));
export const smtpUser = process.env.SMTP_USER || config.get('Smtp.user');
export const smtpPass = process.env.SMTP_PASS || config.get('Smtp.pass');
export const smtpSenderName = process.env.SMTP_SENDER_NAME || config.get('Smtp.senderName');
