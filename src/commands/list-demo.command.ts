import { Injectable } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';
import { Seeder } from '../features/seed/seed.service';

@Command({
  name: 'list-demo',
  description: 'List all demo accounts with passwords',
})
@Injectable()
export class ListDemoCommand extends CommandRunner {
  constructor(private readonly seeder: Seeder) {
    super();
  }

  async run(): Promise<void> {
    await this.seeder.listDemoAccounts();
  }
}
