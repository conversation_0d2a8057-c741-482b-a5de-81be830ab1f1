import { Injectable } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';
import { readdirSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

@Command({
  name: 'list-seed-credentials',
  description: 'List saved seed account credentials from temporary files',
})
@Injectable()
export class ListSeedCredentialsCommand extends CommandRunner {
  async run(): Promise<void> {
    const tempDir = join(process.cwd(), 'temp');
    
    if (!existsSync(tempDir)) {
      console.log('❌ No temporary credentials directory found');
      console.log('💡 Run seed commands to generate credentials files');
      return;
    }

    try {
      const files = readdirSync(tempDir)
        .filter(file => file.startsWith('seed-credentials-') && file.endsWith('.json'))
        .sort((a, b) => a.localeCompare(b))
        .reverse(); // Most recent first

      if (files.length === 0) {
        console.log('❌ No seed credentials files found');
        console.log('💡 Run "npm run seed" or "npm run cli seed" to generate credentials');
        return;
      }

      console.log(`🔑 Found ${files.length} seed credentials file(s):\n`);

      for (const file of files) {
        const filepath = join(tempDir, file);
        const data = JSON.parse(readFileSync(filepath, 'utf8'));
        
        console.log(`📄 File: ${file}`);
        console.log(`📅 Created: ${new Date(data.timestamp).toLocaleString()}`);
        console.log(`🌍 Environment: ${data.environment}`);
        console.log(`⚠️  Warning: ${data.warning}`);
        console.log('👥 Credentials:');
        
        data.credentials.forEach((cred: any) => {
          const roleIcon = cred.role === 'CLINICIAN' ? '👨‍⚕️' : '👤';
          console.log(`  ${roleIcon} ${cred.username}: ${cred.password} (${cred.role})`);
        });
        
        console.log(''); // Empty line between files
      }

      console.log('🗑️  To clean up credentials files, run: npm run clean:seed-credentials');
      
    } catch (error) {
      console.error('❌ Error reading credentials files:', error.message);
    }
  }
}
