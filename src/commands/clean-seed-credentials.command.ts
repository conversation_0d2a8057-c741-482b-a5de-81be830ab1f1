import { Injectable } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';
import { readdirSync, unlinkSync, existsSync, rmdirSync } from 'fs';
import { join } from 'path';

@Command({
  name: 'clean-seed-credentials',
  description: 'Clean up temporary seed credentials files',
})
@Injectable()
export class CleanSeedCredentialsCommand extends CommandRunner {
  async run(): Promise<void> {
    const tempDir = join(process.cwd(), 'temp');
    
    if (!existsSync(tempDir)) {
      console.log('✅ No temporary credentials directory found - nothing to clean');
      return;
    }

    try {
      const files = readdirSync(tempDir)
        .filter(file => file.startsWith('seed-credentials-') && file.endsWith('.json'));

      if (files.length === 0) {
        console.log('✅ No seed credentials files found - nothing to clean');
        return;
      }

      console.log(`🗑️  Found ${files.length} seed credentials file(s) to delete:`);

      for (const file of files) {
        const filepath = join(tempDir, file);
        unlinkSync(filepath);
        console.log(`  ✅ Deleted: ${file}`);
      }

      // Try to remove temp directory if it's empty
      try {
        const remainingFiles = readdirSync(tempDir);
        if (remainingFiles.length === 0) {
          rmdirSync(tempDir);
          console.log('✅ Removed empty temp directory');
        }
      } catch (error) {
        // Directory not empty or other error - ignore
      }

      console.log(`\n✅ Successfully cleaned up ${files.length} credentials file(s)`);
      console.log('🔒 Sensitive credential data has been securely removed');
      
    } catch (error) {
      console.error('❌ Error cleaning credentials files:', error.message);
    }
  }
}
