import { Module } from '@nestjs/common';
import { SeedModule } from '../features/seed/seed.module';
import { CleanSeedCredentialsCommand } from './clean-seed-credentials.command';
import { ListDemoCommand } from './list-demo.command';
import { ListSeedCredentialsCommand } from './list-seed-credentials.command';
import { SeedCommand } from './seed.command';

@Module({
  imports: [SeedModule],
  providers: [
    SeedCommand,
    ListDemoCommand,
    ListSeedCredentialsCommand,
    CleanSeedCredentialsCommand
  ],
})
export class CommandModule {}
