import { Injectable } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';
import { Seeder } from '../features/seed/seed.service';

@Command({
  name: 'seed',
  description: 'Seed the database with sample data',
})
@Injectable()
export class SeedCommand extends CommandRunner {
  constructor(private readonly seeder: Seeder) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    if (options?.users) {
      await this.seeder.seedUsers();
    } else if (options?.demo) {
      await this.seeder.seedDemoAccounts();
    } else {
      await this.seeder.seedAll();
    }
  }

  @Option({
    flags: '-u, --users',
    description: 'Seed only users (admins, clinicians, patients)',
  })
  parseUsers(val: string): boolean {
    return true;
  }

  @Option({
    flags: '-d, --demo',
    description: 'Seed only demo accounts',
  })
  parseDemo(val: string): boolean {
    return true;
  }
}
