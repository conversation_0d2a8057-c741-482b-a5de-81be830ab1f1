import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { SecurityService } from '@aos-shared/services/security.service';
import { CanActivate, ExecutionContext, Inject, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { jwtSecret } from 'app.config';
import { Model } from 'mongoose';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    private readonly jwtService: JwtService,
    private readonly securityService: SecurityService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const token = req.headers?.authorization?.split(' ')[1];
    const ip = this.getClientIp(req);
    const userAgent = req.headers['user-agent'] || 'Unknown';

    if (!token) {
      this.logger.warn(`Missing token - IP: ${ip}, UserAgent: ${userAgent}, Path: ${req.path}`);
      return false;
    }

    // Check if token is blacklisted
    if (this.securityService.isBlacklisted(token)) {
      this.logger.warn(`Blacklisted token used - IP: ${ip}, UserAgent: ${userAgent}, Path: ${req.path}`);
      return false;
    }

    const jwtPayload = this.jwtService.decode(token);
    const username = jwtPayload.username;

    if (!username) {
      this.logger.warn(`Invalid token payload - IP: ${ip}, UserAgent: ${userAgent}, Path: ${req.path}`);
      return false;
    }

    try {
      const user = await this.userModel.findOne({ username });

      if (!user) {
        this.logger.warn(`User not found for token - Username: ${username}, IP: ${ip}, UserAgent: ${userAgent}`);
        return false;
      }

      if (user.isArchived) {
        this.logger.warn(`Archived user attempted access - Username: ${username}, IP: ${ip}, UserAgent: ${userAgent}`);
        return false;
      }

      await this.jwtService.verify(token, {
        secret: jwtSecret,
      });

      // Security logging for successful authentication
      this.securityService.logSecurityEvent('TOKEN_VALIDATED', {
        username,
        ip,
        userAgent,
        path: req.path,
      });

      delete user.password;
      req.user = user;
      return true;
    } catch (error) {
      this.logger.warn(
        `Token verification failed - Username: ${username}, IP: ${ip}, UserAgent: ${userAgent}, Error: ${error.message}`,
      );

      // Log potential token manipulation attempts
      if (error.name === 'JsonWebTokenError') {
        this.securityService.logSecurityEvent(
          'TOKEN_MANIPULATION_ATTEMPT',
          {
            username,
            ip,
            userAgent,
            error: error.message,
            path: req.path,
          },
          'warn',
        );
      }

      return false;
    }
  }

  private getClientIp(req: any): string {
    return (
      req.headers['x-forwarded-for']?.split(',')[0] ||
      req.headers['x-real-ip'] ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'Unknown'
    );
  }
}
