import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { UserRole } from '@aos-enum/user-role.enum';
import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Model } from 'mongoose';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const userEntity = await this.userModel
      .findOne({
        _id: user._id,
      })
      .exec();

    if (!userEntity || userEntity.isArchived) {
      return false;
    }

    const permissions = this.reflector.get<UserRole[]>('permission', context.getHandler());
    if (!permissions || permissions.length === 0) {
      return true;
    }

    if (user && user.role == UserRole.PATIENT && typeof request.headers.firebasetoken !== 'undefined') {
      userEntity.deviceTokens = [request.headers.firebasetoken];

      await userEntity.save();
    }

    const hasPermission = () => permissions.includes(user.role);
    return user?.role && hasPermission();
  }
}
