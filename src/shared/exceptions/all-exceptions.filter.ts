import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeEnv } from '../enums/node-env.enum';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    // Extract request information for security logging
    const ip = this.getClientIp(request);
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const method = request.method;
    const url = request.url;
    const user = request.user?.username || 'Anonymous';

    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      // Log security-relevant exceptions
      if (this.isSecurityRelevant(status, url)) {
        this.logger.warn(
          `Security Exception - Status: ${status}, User: ${user}, IP: ${ip}, Method: ${method}, URL: ${url}, UserAgent: ${userAgent}, Error: ${exception.message}`,
        );
      }

      // Return sanitized error response
      const errorResponse = this.sanitizeErrorResponse(exceptionResponse, status);
      response.status(status).json(errorResponse);
    } else {
      // Log all internal server errors as security events
      const errorDetails = this.extractErrorDetails(exception);
      this.logger.error(
        `Internal Server Error - User: ${user}, IP: ${ip}, Method: ${method}, URL: ${url}, UserAgent: ${userAgent}, Error: ${errorDetails}`,
      );

      // Return generic error message in production
      const errorResponse = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: process.env.NODE_ENV === NodeEnv.PRODUCTION ? 'Internal server error' : errorDetails,
        timestamp: new Date().toISOString(),
        path: url,
      };

      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  private extractErrorDetails(exception: unknown): string {
    if (exception instanceof Error) {
      // For Error objects, use message and optionally stack trace
      const message = exception.message || 'Unknown error';
      const stack = process.env.NODE_ENV !== NodeEnv.PRODUCTION ? exception.stack : '';
      return stack ? `${message}\nStack: ${stack}` : message;
    }

    if (typeof exception === 'string') {
      return exception;
    }

    if (typeof exception === 'object' && exception !== null) {
      try {
        // Try to extract meaningful information from the object
        if ('message' in exception) {
          return (exception as any).message;
        }
        if ('error' in exception) {
          return (exception as any).error;
        }
        // As a last resort, use JSON.stringify for objects
        return JSON.stringify(exception);
      } catch (jsonError) {
        return 'Error object could not be serialized';
      }
    }

    // For primitive values or null/undefined
    return String(exception);
  }

  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'Unknown'
    );
  }

  private isSecurityRelevant(status: number, url: string): boolean {
    // Log authentication, authorization, and suspicious activity
    const securityStatuses = [401, 403, 429];
    const securityPaths = ['/auth', '/login', '/admin'];

    return securityStatuses.includes(status) || securityPaths.some((path) => url.includes(path));
  }

  private sanitizeErrorResponse(exceptionResponse: any, status: number): any {
    if (typeof exceptionResponse === 'string') {
      return {
        statusCode: status,
        error: exceptionResponse,
        timestamp: new Date().toISOString(),
      };
    }

    // Remove sensitive information from error responses in production
    if (process.env.NODE_ENV === NodeEnv.PRODUCTION) {
      const { message, error, statusCode } = exceptionResponse;
      return {
        statusCode: statusCode || status,
        error: error || 'An error occurred',
        message: Array.isArray(message) ? message : [message || 'An error occurred'],
        timestamp: new Date().toISOString(),
      };
    }

    return {
      ...exceptionResponse,
      timestamp: new Date().toISOString(),
    };
  }
}
