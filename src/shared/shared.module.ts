import { DatabaseModule } from '@aos-database/database.module';
import { IsEmailAlreadyExistConstraint } from '@aos-validator/is-email-already-exist.validator';
import { Module } from '@nestjs/common';
import { LoggerModule } from '../logger/logger.module';
import { FirebaseService } from './services/firebase.service';
import { MailerService } from './services/mailer.service';
import { SchedulesService } from './services/schedules.service';
import { SecurityService } from './services/security.service';
// SendGrid now used directly in services
import { IsEmailNotExistConstraint } from '@aos-validator/is-email-not-exist.validator';
import { IsResetPasswordTokenExistValidator } from '@aos-validator/is-reset-password-token-exist.validator';
import { IsResetPasswordTokenNotExpiredValidator } from '@aos-validator/is-reset-password-token-not-expired.validator';
import { IsUserActivatedValidator } from '@aos-validator/is-user-activated.validator';
import { CalculatorService } from '../features/test-results/calculator.service';
import { FlagLoggingService } from '../features/test-results/flag-logging.service';
import { RedFlagEmailService } from '../features/test-results/red-flag-email.service';
import { TestResultService } from '../features/test-results/test-result.service';
import { PregnancyMilestoneService } from '../features/tests/pregnancy-milestone.service';
import { TestService } from '../features/tests/test.service';
import { AppConfigService } from './services/app-config.service';
import { DeepLinkService } from './services/deep-link.service';
import { EmailService } from './services/email.service';
import { HtmlSanitizerService } from './services/html-sanitizer.service';
import { NotificationSchedulerService } from './services/notification-scheduler.service';
import { TimezoneService } from './services/timezone.service';

@Module({
  imports: [DatabaseModule, LoggerModule.forRoot()],
  providers: [
    IsEmailAlreadyExistConstraint,
    IsEmailNotExistConstraint,
    IsUserActivatedValidator,
    IsResetPasswordTokenExistValidator,
    IsResetPasswordTokenNotExpiredValidator,
    MailerService,
    FirebaseService,
    SchedulesService,
    SecurityService,
    TestService,
    EmailService,
    TestResultService,
    CalculatorService,
    FlagLoggingService,
    PregnancyMilestoneService,
    AppConfigService,
    DeepLinkService,
    HtmlSanitizerService,
    NotificationSchedulerService,
    TimezoneService,
    RedFlagEmailService,
    {
      provide: 'RedFlagEmailService',
      useClass: RedFlagEmailService,
    },
  ],
  exports: [PregnancyMilestoneService, SecurityService, TestService, TestResultService, CalculatorService, FlagLoggingService, AppConfigService, DeepLinkService, HtmlSanitizerService, NotificationSchedulerService, TimezoneService, RedFlagEmailService],
})
export class SharedModule {}
