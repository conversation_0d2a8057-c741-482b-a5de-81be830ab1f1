import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { AosCollections, AppConfigType, getCollectionToken } from '../../database/database.constant';
import { HtmlSanitizerService } from './html-sanitizer.service';

@Injectable()
export class AppConfigService {
  constructor(
    @Inject(getCollectionToken(AosCollections.AppConfig))
    private readonly appConfigModel: Model<any>,
    private readonly htmlSanitizerService: HtmlSanitizerService,
  ) {}

  async getRedFlagEmail(): Promise<string> {
    const config = await this.appConfigModel.findOne({ key: AppConfigType.RED_FLAG_EMAIL });
    return config?.value || '<EMAIL>';
  }

  async setRedFlagEmail(email: string, updatedBy: string): Promise<void> {
    // Validate email format using RFC 5322 compliant regex
    if (!this.isValidEmail(email)) {
      throw new Error('Invalid email format provided');
    }

    // Sanitize email to prevent XSS
    const sanitizedEmail = this.sanitizeEmail(email);

    await this.appConfigModel.findOneAndUpdate(
      { key: AppConfigType.RED_FLAG_EMAIL },
      { value: sanitizedEmail, updatedBy, updatedAt: new Date() },
      { upsert: true }
    );
  }

  async getConfig(key: string): Promise<string | null> {
    const config = await this.appConfigModel.findOne({ key });
    return config?.value || null;
  }

  async setConfig(key: string, value: string, updatedBy: string, description?: string): Promise<void> {
    await this.appConfigModel.findOneAndUpdate(
      { key },
      { value, updatedBy, updatedAt: new Date(), ...(description && { description }) },
      { upsert: true }
    );
  }

  /**
   * Validate email format using RFC 5322 compliant regex
   */
  private isValidEmail(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false;
    }

    // RFC 5322 compliant email regex (simplified but robust)
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
  }

  /**
   * Sanitize email to prevent XSS attacks
   * Uses secure HTML sanitizer to prevent ReDoS vulnerabilities
   */
  private sanitizeEmail(email: string): string {
    if (!email || typeof email !== 'string') {
      return '';
    }

    // Use secure HTML sanitizer to remove HTML tags (ReDoS-safe)
    let sanitized = this.htmlSanitizerService.stripHtmlTags(email, {
      preserveLinks: false,
      preserveLineBreaks: false
    });

    // Remove potentially dangerous characters for email context
    sanitized = sanitized.replace(/[<>'"&]/g, '');

    // Trim whitespace and normalize to lowercase
    return sanitized.trim().toLowerCase();
  }
}
