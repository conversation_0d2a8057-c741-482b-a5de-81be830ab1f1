import { Injectable, Logger } from '@nestjs/common';
import { NodeEnv } from '../enums/node-env.enum';

export interface SecurityPolicy {
  authentication: {
    maxLoginAttempts: number;
    lockoutDuration: number; // in minutes
    passwordMinLength: number;
    passwordRequireSpecialChar: boolean;
    passwordRequireNumbers: boolean;
    passwordRequireUppercase: boolean;
    passwordRequireLowercase: boolean;
    tokenExpiration: string;
    refreshTokenExpiration: string;
  };
  rateLimiting: {
    globalRateLimit: number;
    globalWindowMs: number;
    authRateLimit: number;
    authWindowMs: number;
    strictRateLimit: number;
    strictWindowMs: number;
  };
  input: {
    maxRequestSize: string;
    maxFieldSize: string;
    allowedFileTypes: string[];
    maxFileSize: number;
    sanitizeInput: boolean;
  };
  logging: {
    logFailedAttempts: boolean;
    logSuccessfulLogins: boolean;
    logSuspiciousActivity: boolean;
    logLevel: 'error' | 'warn' | 'log' | 'debug';
  };
  cors: {
    allowedOrigins: string[];
    allowCredentials: boolean;
    maxAge: number;
  };
  headers: {
    hsts: boolean;
    hstsMaxAge: number;
    contentSecurityPolicy: boolean;
    xFrameOptions: string;
    xContentTypeOptions: boolean;
  };
}

@Injectable()
export class SecurityConfigService {
  private readonly logger = new Logger(SecurityConfigService.name);

  private readonly defaultPolicy: SecurityPolicy = {
    authentication: {
      maxLoginAttempts: 5,
      lockoutDuration: 15, // 15 minutes
      passwordMinLength: 8,
      passwordRequireSpecialChar: true,
      passwordRequireNumbers: true,
      passwordRequireUppercase: true,
      passwordRequireLowercase: true,
      tokenExpiration: '1h',
      refreshTokenExpiration: '7d',
    },
    rateLimiting: {
      globalRateLimit: 100,
      globalWindowMs: 15 * 60 * 1000, // 15 minutes
      authRateLimit: 5,
      authWindowMs: 15 * 60 * 1000, // 15 minutes
      strictRateLimit: 10,
      strictWindowMs: 60 * 1000, // 1 minute
    },
    input: {
      maxRequestSize: '10mb',
      maxFieldSize: '1mb',
      allowedFileTypes: ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx'],
      maxFileSize: 5 * 1024 * 1024, // 5MB
      sanitizeInput: true,
    },
    logging: {
      logFailedAttempts: true,
      logSuccessfulLogins: true,
      logSuspiciousActivity: true,
      logLevel: process.env.NODE_ENV === NodeEnv.PRODUCTION ? 'warn' : 'debug',
    },
    cors: {
      allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [
        'http://localhost:3000',
        'https://fe-aos-dev.b13devops.com',
        'https://fe-aos-test.b13devops.com',
        'https://fe-aos-uat.b13devops.com',
      ],
      allowCredentials: true,
      maxAge: 86400, // 24 hours
    },
    headers: {
      hsts: true,
      hstsMaxAge: 31536000, // 1 year
      contentSecurityPolicy: true,
      xFrameOptions: 'DENY',
      xContentTypeOptions: true,
    },
  };

  private currentPolicy: SecurityPolicy;

  constructor() {
    this.currentPolicy = this.loadSecurityPolicy();
    this.validatePolicy(this.currentPolicy);
    this.logger.log('Security policy loaded and validated');
  }

  getPolicy(): SecurityPolicy {
    return { ...this.currentPolicy };
  }

  getAuthPolicy() {
    return this.currentPolicy.authentication;
  }

  getRateLimitPolicy() {
    return this.currentPolicy.rateLimiting;
  }

  getInputPolicy() {
    return this.currentPolicy.input;
  }

  getLoggingPolicy() {
    return this.currentPolicy.logging;
  }

  getCorsPolicy() {
    return this.currentPolicy.cors;
  }

  getHeadersPolicy() {
    return this.currentPolicy.headers;
  }

  isFileTypeAllowed(fileName: string): boolean {
    const ext = this.getFileExtension(fileName);
    return this.currentPolicy.input.allowedFileTypes.includes(ext);
  }

  isFileSizeAllowed(fileSize: number): boolean {
    return fileSize <= this.currentPolicy.input.maxFileSize;
  }

  shouldLogSecurityEvent(eventType: string): boolean {
    switch (eventType) {
      case 'FAILED_LOGIN':
        return this.currentPolicy.logging.logFailedAttempts;
      case 'SUCCESSFUL_LOGIN':
        return this.currentPolicy.logging.logSuccessfulLogins;
      case 'SUSPICIOUS_ACTIVITY':
        return this.currentPolicy.logging.logSuspiciousActivity;
      default:
        return true;
    }
  }

  getMaxLoginAttempts(): number {
    return this.currentPolicy.authentication.maxLoginAttempts;
  }

  getLockoutDuration(): number {
    return this.currentPolicy.authentication.lockoutDuration;
  }

  updatePolicy(newPolicy: Partial<SecurityPolicy>): void {
    this.currentPolicy = {
      ...this.currentPolicy,
      ...newPolicy,
    };

    this.validatePolicy(this.currentPolicy);
    this.logger.warn(`Security policy updated: ${JSON.stringify(newPolicy)}`);
  }

  getPasswordRequirements(): {
    minLength: number;
    requireSpecialChar: boolean;
    requireNumbers: boolean;
    requireUppercase: boolean;
    requireLowercase: boolean;
  } {
    return {
      minLength: this.currentPolicy.authentication.passwordMinLength,
      requireSpecialChar: this.currentPolicy.authentication.passwordRequireSpecialChar,
      requireNumbers: this.currentPolicy.authentication.passwordRequireNumbers,
      requireUppercase: this.currentPolicy.authentication.passwordRequireUppercase,
      requireLowercase: this.currentPolicy.authentication.passwordRequireLowercase,
    };
  }

  isStrictModeEnabled(): boolean {
    return process.env.NODE_ENV === NodeEnv.PRODUCTION;
  }

  private loadSecurityPolicy(): SecurityPolicy {
    // In production, you might load this from a database or config service
    // For now, use environment variables to override defaults

    const policy = { ...this.defaultPolicy };

    // Override with environment variables if present
    if (process.env.MAX_LOGIN_ATTEMPTS) {
      policy.authentication.maxLoginAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS);
    }

    if (process.env.LOCKOUT_DURATION) {
      policy.authentication.lockoutDuration = parseInt(process.env.LOCKOUT_DURATION);
    }

    if (process.env.PASSWORD_MIN_LENGTH) {
      policy.authentication.passwordMinLength = parseInt(process.env.PASSWORD_MIN_LENGTH);
    }

    if (process.env.GLOBAL_RATE_LIMIT) {
      policy.rateLimiting.globalRateLimit = parseInt(process.env.GLOBAL_RATE_LIMIT);
    }

    if (process.env.AUTH_RATE_LIMIT) {
      policy.rateLimiting.authRateLimit = parseInt(process.env.AUTH_RATE_LIMIT);
    }

    return policy;
  }

  private validatePolicy(policy: SecurityPolicy): void {
    // Validate authentication settings
    if (policy.authentication.maxLoginAttempts < 1) {
      throw new Error('Max login attempts must be at least 1');
    }

    if (policy.authentication.lockoutDuration < 1) {
      throw new Error('Lockout duration must be at least 1 minute');
    }

    if (policy.authentication.passwordMinLength < 6) {
      throw new Error('Password minimum length must be at least 6');
    }

    // Validate rate limiting
    if (policy.rateLimiting.globalRateLimit < 1) {
      throw new Error('Global rate limit must be at least 1');
    }

    if (policy.rateLimiting.authRateLimit < 1) {
      throw new Error('Auth rate limit must be at least 1');
    }

    // Validate file settings
    if (policy.input.maxFileSize < 1024) {
      throw new Error('Max file size must be at least 1KB');
    }

    this.logger.log('Security policy validation passed');
  }

  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot).toLowerCase() : '';
  }

  // Security policy enforcement methods
  enforceStrictMode(): boolean {
    return this.isStrictModeEnabled();
  }

  getSecurityLevel(): 'low' | 'medium' | 'high' | 'strict' {
    if (this.isStrictModeEnabled()) {
      return 'strict';
    } else if (this.currentPolicy.authentication.maxLoginAttempts <= 3) {
      return 'high';
    } else if (this.currentPolicy.authentication.maxLoginAttempts <= 5) {
      return 'medium';
    } else {
      return 'low';
    }
  }
}
