import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);
  private readonly tokenBlacklist = new Set<string>();

  /**
   * Add token to blacklist for logout/revocation
   */
  addToBlacklist(token: string): void {
    this.tokenBlacklist.add(token);
    this.logger.warn(`Token added to blacklist: ${token.substring(0, 10)}...`);
  }

  /**
   * Check if token is blacklisted
   */
  isBlacklisted(token: string): boolean {
    return this.tokenBlacklist.has(token);
  }

  /**
   * Clear expired tokens from blacklist (should be called periodically)
   */
  clearExpiredTokens(): void {
    // In production, implement proper token expiration check
    // For now, clear all tokens older than 24 hours
    this.tokenBlacklist.clear();
    this.logger.log('Cleared expired tokens from blacklist');
  }

  /**
   * Log failed login attempts for security monitoring
   */
  logFailedLogin(username: string, ip: string, userAgent?: string): void {
    this.logger.warn(`Failed login attempt - Username: ${username}, IP: ${ip}, UserAgent: ${userAgent || 'Unknown'}`);
  }

  /**
   * Log successful login for audit trail
   */
  logSuccessfulLogin(username: string, ip: string, userAgent?: string): void {
    this.logger.log(`Successful login - Username: ${username}, IP: ${ip}, UserAgent: ${userAgent || 'Unknown'}`);
  }

  /**
   * Log security-related events
   */
  logSecurityEvent(event: string, details: any, level: 'log' | 'warn' | 'error' = 'log'): void {
    const message = `Security Event: ${event} - ${JSON.stringify(details)}`;

    switch (level) {
      case 'warn':
        this.logger.warn(message);
        break;
      case 'error':
        this.logger.error(message);
        break;
      default:
        this.logger.log(message);
    }
  }

  /**
   * Generate secure random string for tokens/secrets
   */
  generateSecureRandom(length: number = 32): string {
    const crypto = require('crypto');
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash sensitive data for logging (removes sensitive info but keeps format for debugging)
   */
  hashForLogging(sensitive: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(sensitive).digest('hex').substring(0, 8) + '...';
  }

  /**
   * Check if request is suspicious (rate limiting, unusual patterns)
   */
  isSuspiciousRequest(req: any): boolean {
    // Add your suspicious request detection logic here
    // Example: Multiple failed attempts, unusual headers, etc.

    // Check for suspicious user agents
    const suspiciousUserAgents = ['curl', 'wget', 'python-requests', 'scanner'];
    const userAgent = req.headers['user-agent']?.toLowerCase() || '';

    if (suspiciousUserAgents.some((agent) => userAgent.includes(agent))) {
      return true;
    }

    // Check for missing common headers
    if (!req.headers.accept || !req.headers['user-agent']) {
      return true;
    }

    return false;
  }
}
