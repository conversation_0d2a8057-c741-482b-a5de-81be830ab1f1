import { Injectable, Logger } from '@nestjs/common';
import { sendMail } from '@aos-shared/helpers/node-mailer.helper';
import * as moment from 'moment';

export interface EmailRetryOptions {
  maxRetries?: number;
  retryIntervalMinutes?: number;
}

@Injectable()
export class EmailRetryService {
  private readonly logger = new Logger(EmailRetryService.name);
  private readonly DEFAULT_MAX_RETRIES = 3;
  private readonly DEFAULT_RETRY_INTERVAL_MINUTES = 5;

  /**
   * Send email with retry logic
   * @param email Email address to send to
   * @param subject Email subject
   * @param content Email content (HTML)
   * @param options Retry options
   * @returns Promise<boolean> - true if email sent successfully, false otherwise
   */
  async sendEmailWithRetry(
    email: string,
    subject: string,
    content: string,
    options: EmailRetryOptions = {}
  ): Promise<boolean> {
    const maxRetries = options.maxRetries || this.DEFAULT_MAX_RETRIES;
    const retryIntervalMinutes = options.retryIntervalMinutes || this.DEFAULT_RETRY_INTERVAL_MINUTES;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`Attempting to send email to ${email} (attempt ${attempt}/${maxRetries})`);
        
        await sendMail(email, subject, content);
        
        this.logger.log(`Email sent successfully to ${email} on attempt ${attempt}`);
        return true;
      } catch (error) {
        this.logger.error(`Email send attempt ${attempt} failed for ${email}: ${error.message}`);
        
        if (attempt < maxRetries) {
          this.logger.log(`Waiting ${retryIntervalMinutes} minutes before retry...`);
          await this.delay(retryIntervalMinutes * 60 * 1000); // Convert minutes to milliseconds
        } else {
          this.logger.error(`All ${maxRetries} email attempts failed for ${email}`);
        }
      }
    }

    return false;
  }

  /**
   * Send emails to multiple recipients with retry logic
   * @param emails Array of email addresses
   * @param subject Email subject
   * @param content Email content (HTML)
   * @param options Retry options
   * @returns Promise<{successful: string[], failed: string[]}> - Results of email sending
   */
  async sendEmailsWithRetry(
    emails: string[],
    subject: string,
    content: string,
    options: EmailRetryOptions = {}
  ): Promise<{successful: string[], failed: string[]}> {
    const successful: string[] = [];
    const failed: string[] = [];

    for (const email of emails) {
      const result = await this.sendEmailWithRetry(email, subject, content, options);
      if (result) {
        successful.push(email);
      } else {
        failed.push(email);
      }
    }

    return { successful, failed };
  }

  /**
   * Check if enough time has passed since last email attempt
   * @param lastAttempt Date of last email attempt
   * @param intervalMinutes Minimum interval between attempts in minutes
   * @returns boolean - true if enough time has passed
   */
  canRetryEmail(lastAttempt: Date, intervalMinutes: number = this.DEFAULT_RETRY_INTERVAL_MINUTES): boolean {
    if (!lastAttempt) {
      return true;
    }

    const now = moment().utc();
    const lastAttemptMoment = moment(lastAttempt).utc();
    const minutesSinceLastAttempt = now.diff(lastAttemptMoment, 'minutes');

    return minutesSinceLastAttempt >= intervalMinutes;
  }

  /**
   * Delay execution for specified milliseconds
   * @param ms Milliseconds to delay
   * @returns Promise<void>
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log email retry attempt
   * @param email Email address
   * @param attempt Current attempt number
   * @param maxRetries Maximum retry attempts
   * @param error Error message (if any)
   */
  logEmailAttempt(email: string, attempt: number, maxRetries: number, error?: string): void {
    if (error) {
      this.logger.error(`Email attempt ${attempt}/${maxRetries} failed for ${email}: ${error}`);
    } else {
      this.logger.log(`Email attempt ${attempt}/${maxRetries} successful for ${email}`);
    }
  }
}
