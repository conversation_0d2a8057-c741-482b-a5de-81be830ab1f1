import { Injectable, Logger } from '@nestjs/common';
import { cronTimezone } from '../../app.config';

@Injectable()
export class TimezoneService {
  private readonly logger = new Logger(TimezoneService.name);
  private readonly ukTimezone = cronTimezone;

  /**
   * Get current UK time (handles GMT/BST automatically)
   */
  getCurrentUKTime(): Date {
    return new Date(new Date().toLocaleString('en-US', { timeZone: this.ukTimezone }));
  }

  /**
   * Get formatted UK time string
   */
  getFormattedUKTime(): string {
    return new Date().toLocaleString('en-GB', {
      timeZone: this.ukTimezone,
      hour12: false,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  }

  /**
   * Check if UK is currently in BST (British Summer Time)
   */
  isUKInBST(): boolean {
    const now = new Date();
    const ukTime = new Date(now.toLocaleString('en-US', { timeZone: this.ukTimezone }));
    const utcTime = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
    
    // BST is UTC+1, GMT is UTC+0
    const offsetHours = (ukTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60);
    return offsetHours === 1;
  }

  /**
   * Get UK timezone name (GMT or BST)
   */
  getUKTimezoneName(): string {
    return this.isUKInBST() ? 'BST' : 'GMT';
  }

  /**
   * Get UK offset from UTC
   */
  getUKOffset(): string {
    return this.isUKInBST() ? '+01:00' : '+00:00';
  }

  /**
   * Convert any date to UK timezone
   */
  convertToUKTime(date: Date): Date {
    return new Date(date.toLocaleString('en-US', { timeZone: this.ukTimezone }));
  }

  /**
   * Check if current UK time is 9:00 AM (for validation)
   */
  isCurrentlyNineAMUK(): boolean {
    const ukTime = this.getCurrentUKTime();
    return ukTime.getHours() === 9 && ukTime.getMinutes() === 0;
  }

  /**
   * Get next 9 AM UK time
   */
  getNext9AMUK(): Date {
    const ukTime = this.getCurrentUKTime();
    const next9AM = new Date(ukTime);
    
    next9AM.setHours(9, 0, 0, 0);
    
    // If it's already past 9 AM today, set to tomorrow
    if (ukTime.getHours() >= 9) {
      next9AM.setDate(next9AM.getDate() + 1);
    }
    
    return next9AM;
  }

  /**
   * Log timezone information for debugging
   */
  logTimezoneInfo(): void {
    const ukTime = this.getFormattedUKTime();
    const timezoneName = this.getUKTimezoneName();
    const offset = this.getUKOffset();
    const isBST = this.isUKInBST();
    
    this.logger.log(`🌍 Timezone Configuration:`);
    this.logger.log(`   📍 Configured timezone: ${this.ukTimezone}`);
    this.logger.log(`   🕘 Current UK time: ${ukTime}`);
    this.logger.log(`   🏷️  Current timezone: ${timezoneName} (UTC${offset})`);
    this.logger.log(`   ☀️  British Summer Time: ${isBST ? 'Yes' : 'No'}`);
    this.logger.log(`   ⏰ Next 9 AM UK: ${this.getNext9AMUK().toLocaleString('en-GB', { timeZone: this.ukTimezone })}`);
  }

  /**
   * Validate cron job execution time
   */
  validateCronExecution(): boolean {
    const ukTime = this.getCurrentUKTime();
    const hour = ukTime.getHours();
    const minute = ukTime.getMinutes();
    
    // Allow some tolerance (within 5 minutes of 9:00 AM)
    const isValid = hour === 9 && minute >= 0 && minute <= 5;
    
    if (!isValid) {
      this.logger.warn(`⚠️  Cron job executed at unexpected time: ${this.getFormattedUKTime()}`);
    } else {
      this.logger.log(`✅ Cron job executed at correct UK time: ${this.getFormattedUKTime()}`);
    }
    
    return isValid;
  }
}
