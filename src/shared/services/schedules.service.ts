import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { cronTestOverDate, cronTimezone } from 'app.config';
import { TestResultService } from '../../features/test-results/test-result.service';
import { NotificationSchedulerService } from './notification-scheduler.service';
import { TimezoneService } from './timezone.service';

@Injectable()
export class SchedulesService {
  constructor(
    private readonly testResultService: TestResultService,
    private readonly timezoneService: TimezoneService,
  ) {}

  private readonly logger = new Logger(SchedulesService.name);

  @Cron(cronTestOverDate, {
    timeZone: cronTimezone,
  })
  async sendDueDateReminder() {
    // Log timezone information and validate execution time
    this.timezoneService.logTimezoneInfo();
    this.timezoneService.validateCronExecution();

    this.logger.log(`🔔 Triggered cron job at 9 AM UK time (${cronTimezone})`);
    this.logger.log(`🕘 Current UK time: ${this.timezoneService.getFormattedUKTime()}`);

    try {
      // Step 1: Send due date notifications for tests due today
      this.logger.log('📅 Processing due date notifications for tests due today...');
      await this.testResultService.sendDueDateNotifications();

      // Step 2: Process overdue flags and send additional overdue notifications
      this.logger.log('⏰ Processing overdue flag updates...');
      const overdueTests = await this.testResultService.findOverdueTestByType();
      await this.testResultService.triggerOverdueFlag(overdueTests);

      this.logger.log('✅ Completed all notification processing successfully');
    } catch (error) {
      this.logger.error(`❌ Error in notification processing: ${error.message}`);
    }
  }
}
