import { Injectable, Logger } from '@nestjs/common';

/**
 * HTML Sanitization Service
 * Provides ReDoS-safe HTML processing utilities
 * 
 * Security Features:
 * - ReDoS-resistant regex patterns
 * - Input length validation
 * - Timeout protection for large inputs
 * - Comprehensive HTML entity encoding
 */
@Injectable()
export class HtmlSanitizerService {
  private readonly logger = new Logger(HtmlSanitizerService.name);
  
  // Security limits
  private readonly MAX_INPUT_LENGTH = 1000000; // 1MB limit
  private readonly PROCESSING_TIMEOUT = 5000; // 5 second timeout

  /**
   * Safely remove HTML tags from text with ReDoS protection
   * @param html Input HTML string
   * @param options Configuration options
   * @returns Plain text with HTML tags removed
   */
  stripHtmlTags(html: string, options: HtmlStripOptions = {}): string {
    if (!html || typeof html !== 'string') {
      return '';
    }

    // Input validation for security
    if (html.length > this.MAX_INPUT_LENGTH) {
      this.logger.warn(`Input too large for HTML stripping: ${html.length} characters`);
      throw new Error('Input too large for processing');
    }

    const {
      preserveLineBreaks = true,
      preserveLinks = false,
      maxProcessingTime = this.PROCESSING_TIMEOUT
    } = options;

    return this.processWithTimeout(() => {
      let result = html;

      if (preserveLineBreaks) {
        // Convert common line break tags to newlines
        result = result
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<\/p>/gi, '\n\n')
          .replace(/<p[^>]*>/gi, '');
      }

      // Remove HTML comments first (they can contain problematic content)
      result = this.removeHtmlComments(result);

      if (preserveLinks) {
        // Extract link text and URLs before removing tags (ReDoS-safe pattern)
        result = this.preserveLinksSecurely(result);
      }

      // Remove HTML tags using ReDoS-safe pattern
      result = this.removeHtmlTagsSafe(result);

      // Clean up excessive whitespace
      result = result
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .replace(/[ \t]+/g, ' ')
        .trim();

      return result;
    }, maxProcessingTime);
  }

  /**
   * Convert HTML to plain text with enhanced formatting preservation
   */
  htmlToText(html: string): string {
    return this.stripHtmlTags(html, {
      preserveLineBreaks: true,
      preserveLinks: true
    });
  }

  /**
   * Sanitize text for safe inclusion in HTML content
   */
  sanitizeForHtml(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Input length validation
    if (input.length > this.MAX_INPUT_LENGTH) {
      this.logger.warn(`Input too large for HTML sanitization: ${input.length} characters`);
      throw new Error('Input too large for processing');
    }

    return input.replace(/[<>&"']/g, (match) => {
      const htmlEntities: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#x27;'
      };
      return htmlEntities[match] || match;
    });
  }

  /**
   * ReDoS-safe HTML tag removal
   * Uses specific patterns that prevent catastrophic backtracking
   */
  private removeHtmlTagsSafe(text: string): string {
    // Pattern explanation:
    // - <\/?[a-zA-Z][a-zA-Z0-9]*  : Opening/closing tag with valid tag name
    // - (?:\s[^>]*)?              : Optional attributes (non-capturing, non-greedy)
    // - >                         : Closing bracket
    // This pattern is ReDoS-safe because:
    // 1. Tag names are restricted to alphanumeric
    // 2. Attribute matching is non-greedy and bounded
    // 3. No nested quantifiers that can cause backtracking
    
    const safeTagPattern = /<\/?[a-zA-Z][a-zA-Z0-9]*(?:\s[^>]*)?>/g;
    
    return text.replace(safeTagPattern, '');
  }

  /**
   * Remove HTML comments safely
   */
  private removeHtmlComments(text: string): string {
    // Use non-greedy matching for comments to prevent ReDoS
    return text.replace(/<!--[\s\S]*?-->/g, '');
  }

  /**
   * Preserve links securely without ReDoS vulnerability
   * Converts <a href="url">text</a> to "text (url)"
   */
  private preserveLinksSecurely(text: string): string {
    // ReDoS-safe link pattern using atomic matching approach:
    // - Eliminates catastrophic backtracking through specific character matching
    // - Uses possessive quantifiers simulation via character class restrictions
    // - Avoids nested optional quantifiers that cause exponential backtracking

    // Pattern explanation:
    // <a\s                      : <a followed by exactly one whitespace
    // [^>]*                     : Any attributes (greedy but bounded by >)
    // href\s*=\s*               : href attribute with optional whitespace
    // (["'])([^"']*)\1          : Quoted URL (capturing quote type and URL) - greedy within quotes
    // [^>]*                     : Any remaining attributes (greedy but bounded by >)
    // >                         : Closing bracket
    // ([^<]*)                   : Link text (greedy but bounded by <)
    // <\/a>                     : Closing tag

    // This pattern is ReDoS-safe because:
    // 1. No nested quantifiers - each quantifier operates on different character sets
    // 2. Greedy quantifiers are bounded by specific stop characters (>, <, quotes)
    // 3. No optional groups that can cause backtracking loops
    // 4. Character classes are mutually exclusive ([^>] vs [^<] vs [^"'])

    const safeLinkPattern = /<a\s[^>]*href\s*=\s*(["'])([^"']*)\1[^>]*>([^<]*)<\/a>/gi;

    return text.replace(safeLinkPattern, (match, _quote, url, linkText) => {
      // Sanitize the extracted parts to prevent injection
      const cleanUrl = url.trim();
      const cleanText = linkText.trim();

      // Only preserve if both URL and text are present
      if (cleanUrl && cleanText) {
        return `${cleanText} (${cleanUrl})`;
      }

      // Fallback to just the text if URL is missing/invalid
      return cleanText || match;
    });
  }

  /**
   * Execute function with timeout protection
   */
  private processWithTimeout<T>(fn: () => T, timeoutMs: number): T {
    const startTime = Date.now();
    
    const result = fn();
    
    const processingTime = Date.now() - startTime;
    if (processingTime > timeoutMs) {
      this.logger.warn(`HTML processing took ${processingTime}ms, exceeding timeout of ${timeoutMs}ms`);
    }
    
    return result;
  }

  /**
   * Validate that input doesn't contain patterns that could cause ReDoS
   */
  validateInputSafety(input: string): { safe: boolean; reason?: string } {
    if (!input || typeof input !== 'string') {
      return { safe: true };
    }

    // Check for excessively long sequences that could cause issues
    const suspiciousPatterns = [
      { pattern: /<[^>]{1000,}/, reason: 'Extremely long unclosed tag' },
      { pattern: /<!--[\s\S]{10000,}/, reason: 'Extremely long HTML comment' },
      { pattern: /<[^>]*[<]{10,}/, reason: 'Nested angle brackets in tag' }
    ];

    for (const { pattern, reason } of suspiciousPatterns) {
      if (pattern.test(input)) {
        return { safe: false, reason };
      }
    }

    return { safe: true };
  }
}

export interface HtmlStripOptions {
  preserveLineBreaks?: boolean;
  preserveLinks?: boolean;
  maxProcessingTime?: number;
}
