import { Injectable, Logger } from '@nestjs/common';
import { TestType } from '../../shared/enums/test-type.enum';
import { FirebaseService } from './firebase.service';

@Injectable()
export class NotificationSchedulerService {
  private readonly logger = new Logger(NotificationSchedulerService.name);

  constructor(private readonly firebaseService: FirebaseService) {}

  /**
   * Schedule notification for a test due date
   * Called when test due dates are generated or updated
   */
  async scheduleTestDueNotification(
    patient: any,
    testType: TestType,
    testTitle: string,
    dueDate: Date,
  ): Promise<void> {
    try {
      this.logger.log(
        `Scheduling notification for patient ${patient._id}, test type ${testType}, due date ${dueDate}`,
      );

      // Format test name according to functional requirements
      const testName = this.formatTestName(testType, testTitle);
      
      // Log the scheduling event
      this.logger.log(
        `Notification scheduled: "${testName}" for patient ${patient.username || patient._id} on ${dueDate}`,
      );

      // Note: Actual scheduling will be handled by the cron job at 9 AM
      // This method primarily logs and validates the scheduling request
      
    } catch (error) {
      this.logger.error(
        `Failed to schedule notification for patient ${patient._id}: ${error.message}`,
      );
    }
  }

  /**
   * Send due date notification at 9 AM
   * Called by cron job for tests due today
   */
  async sendTestDueNotification(
    patient: any,
    testType: TestType,
    testTitle: string,
  ): Promise<void> {
    try {
      if (!patient.deviceTokens || patient.deviceTokens.length === 0) {
        this.logger.warn(`No device tokens found for patient ${patient._id}`);
        return;
      }

      // Format test name according to functional requirements
      const testName = this.formatTestName(testType, testTitle);
      const notificationTitle = `Your ${testName} is due today.`;
      const notificationBody = `Your ${testName} is due today. Please check the IBD-PRAM app to complete the test.`;

      // Update badge count
      patient.badge = (patient.badge || 0) + 1;
      await patient.save();

      // Send notification to all device tokens
      for (const token of patient.deviceTokens) {
        this.logger.log(
          `Sending notification to patient ${patient._id} - Token: ${token}`,
        );
        
        await this.firebaseService.pushNotification(
          patient,
          token,
          notificationTitle,
          notificationBody,
          patient.badge,
        );
      }

      this.logger.log(
        `Successfully sent notification: "${notificationTitle}" to patient ${patient.username || patient._id}`,
      );
      
    } catch (error) {
      this.logger.error(
        `Failed to send notification to patient ${patient._id}: ${error.message}`,
      );
    }
  }

  /**
   * Format test name according to functional requirements
   * Maps test types to user-friendly names as specified in requirements
   */
  private formatTestName(testType: TestType, testTitle?: string): string {
    switch (testType) {
      case TestType.SYMPTOMS:
        return 'Symptoms Test';
      case TestType.MENTAL_WELLBEING:
        return 'Mental Wellbeing Test';
      case TestType.GENERAL_WELLBEING:
        return 'General Wellbeing Test';
      default:
        // Fallback to provided title or generic name
        return testTitle || 'Test';
    }
  }

  /**
   * Check if a test is due today
   * Used by cron job to determine which notifications to send
   */
  isDueToday(dueDate: Date): boolean {
    const today = new Date();
    const due = new Date(dueDate);
    
    return (
      due.getFullYear() === today.getFullYear() &&
      due.getMonth() === today.getMonth() &&
      due.getDate() === today.getDate()
    );
  }

  /**
   * Calculate days overdue for a test
   */
  calculateDaysOverdue(dueDate: Date): number {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  /**
   * Get test name for logging and display purposes
   */
  getTestDisplayName(testType: TestType): string {
    return this.formatTestName(testType);
  }
}
