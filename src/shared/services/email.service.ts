import { Injectable, Logger } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { sendGridApiKey } from '../../app.config';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor() {
    sgMail.setApiKey(sendGridApiKey);
  }

  async send(emailData: sgMail.MailDataRequired): Promise<any> {
    try {
      const result = await sgMail.send(emailData);
      this.logger.log('Email sent successfully');
      return result;
    } catch (error) {
      this.logger.error(`Error during send email: ${error.message}`);
      throw error;
    }
  }

  async sendMultiple(emailData: sgMail.MailDataRequired): Promise<any> {
    try {
      const result = await sgMail.sendMultiple(emailData);
      this.logger.log('Multiple emails sent successfully');
      return result;
    } catch (error) {
      this.logger.error(`Error during send multiple emails: ${error.message}`);
      throw error;
    }
  }

  // Legacy method to maintain backward compatibility
  async sendLegacy(
    from: string,
    subject: string,
    tos: string[],
    content: string,
    isHtml: boolean = true,
  ): Promise<any> {
    const emailData: sgMail.MailDataRequired = {
      to: tos,
      from,
      subject,
      ...(isHtml ? { html: content } : { text: content }),
    };

    return this.sendMultiple(emailData);
  }
}
