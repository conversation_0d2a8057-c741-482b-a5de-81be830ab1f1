import { Injectable, Logger } from '@nestjs/common';
import { firebaseDatabaseURL, firebaseServiceAccount } from '../../app.config';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK with secure credential loading
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(firebaseServiceAccount),
    databaseURL: firebaseDatabaseURL,
  });
}

@Injectable()
export class FirebaseService {
  private readonly logger = new Logger(FirebaseService.name);

  /**
   * Send push notification compliant with functional requirements 6.3.7.1
   * Supports app-level push notifications displayed as:
   * - Badges, banners, lock screens, and notification centers
   * - User can disable via device settings (handled by OS)
   * - Tapping notification opens app to Home Page (handled by app)
   *
   * @param user - Patient user object
   * @param fcmToken - Firebase Cloud Messaging token
   * @param title - Notification title (e.g., "Your Symptoms Test is due today.")
   * @param message - Notification body message
   * @param badge - Badge count for app icon
   */
  async pushNotification(user, fcmToken: string, title: string, message: string, badge: number) {
    delete user.password;
    const payload = {
      notification: {
        title,
        body: message,
      },
      data: {
        user: JSON.stringify(user),
      },
      apns: {
        headers: {
          'apns-priority': '10',
          'apns-expiration': '360000',
        },
        payload: {
          aps: {
            alert: {
              title,
              body: message,
            },
            badge,
            sound: 'default',
          },
        },
      },
      token: fcmToken,
    };

    try {
      await admin.messaging().send(payload);
      this.logger.log(`Successfully sent push message ${user.id}`);
    } catch (ex) {
      this.logger.error(`Error when sending message, ${ex.message}`);
    }
  }
}
