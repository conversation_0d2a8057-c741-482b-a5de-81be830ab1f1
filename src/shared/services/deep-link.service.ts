import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { clinicianPortalUrl } from '../../app.config';

@Injectable()
export class DeepLinkService {
  constructor() {}

  generatePatientProfileLink(objectPatientId, patientUsername: string): string {
    const patientId = objectPatientId?.toString() || objectPatientId;

    // Validate patient ID
    if (!patientId || typeof patientId !== 'string' || patientId.trim().length === 0) {
      throw new Error('Invalid patient ID provided');
    }

    // Validate base URL format
    if (!clinicianPortalUrl || !this.isValidUrl(clinicianPortalUrl)) {
      throw new Error('Invalid clinician portal URL configuration');
    }

    // Sanitize patient ID to prevent URL injection
    const sanitizedPatientId = encodeURIComponent(patientId.trim());
    const sanitizedPatientUsername = encodeURIComponent(patientUsername.trim());

    const link = `${clinicianPortalUrl}/landing/patient-test-detail/${sanitizedPatientId}?patientName=${sanitizedPatientUsername}`;
    return link;
  }

  validateDeepLink(token: string, expiryTime: string): boolean {
    try {
      // Validate inputs
      if (!token || typeof token !== 'string') {
        return false;
      }

      if (!expiryTime || typeof expiryTime !== 'string') {
        return false;
      }

      // Validate token format
      if (!this.isValidToken(token)) {
        return false;
      }

      // Check if the link has expired
      const expiryMoment = moment(expiryTime);
      if (!expiryMoment.isValid()) {
        return false;
      }

      const isExpired = moment().isAfter(expiryMoment);
      if (isExpired) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  extractPatientIdFromLink(deepLink: string): string | null {
    try {
      // Validate input
      if (!deepLink || typeof deepLink !== 'string') {
        return null;
      }

      // Handle malformed URLs
      if (!this.isValidUrl(deepLink)) {
        return null;
      }

      const url = new URL(deepLink);
      const pathParts = url.pathname.split('/');
      const patientIndex = pathParts.indexOf('patient');

      if (patientIndex !== -1 && patientIndex + 1 < pathParts.length) {
        const patientId = decodeURIComponent(pathParts[patientIndex + 1]);

        // Validate extracted patient ID
        if (patientId && patientId.trim().length > 0) {
          return patientId.trim();
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  isLinkExpired(expiryTime: string): boolean {
    try {
      return moment().isAfter(moment(expiryTime));
    } catch (error) {
      return true; // Treat invalid expiry time as expired
    }
  }

  // Utility method to get expiry time from query parameters
  getExpiryFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('expires');
    } catch (error) {
      return null;
    }
  }

  // Utility method to get token from query parameters
  getTokenFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('token');
    } catch (error) {
      return null;
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate token format and length
   */
  private isValidToken(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Token should be at least 10 characters and contain only safe characters
    const tokenRegex = /^[a-zA-Z0-9\-_]+$/;
    return token.length >= 10 && token.length <= 255 && tokenRegex.test(token);
  }
}
