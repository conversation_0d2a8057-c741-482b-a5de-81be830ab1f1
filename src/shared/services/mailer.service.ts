import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { Logger } from '../../logger/logger.decorator';
import { LoggerService } from '../../logger/logger.service';
import { smtpSenderName } from './../../app.config';

@Injectable()
export class MailerService {
  private isConfigured = false;

  constructor(@Logger('MailerService') private readonly logger: LoggerService) {
    const apiKey = process.env.SENDGRID_API_KEY || '';

    if (apiKey && apiKey.startsWith('SG.')) {
      sgMail.setApiKey(apiKey);
      this.isConfigured = true;
      this.logger.log('SendGrid configured successfully');
    } else {
      this.logger.log('WARNING: SendGrid API key not configured or invalid. Email functionality will be disabled.');
      this.isConfigured = false;
    }
  }

  /**
   * @param toEmail
   * @param fromEmail
   * @param subject
   * @param text
   * @param html
   * @param attachments
   * @param cc
   * const attachments = [
   */
  //   {
  //     content: (await fsPromises.readFile(seeder.clinicianImportPath)).toString('base64'),
  //     // tslint:disable-next-line:jsdoc-format
  //     filename: 'clinicians.csv',
  //     type: 'csv',
  //     disposition: 'attachment',
  //     content_id: 'mytext'
  //   }
  // ];
  async sendMail(
    toEmail: string,
    fromEmail: string,
    subject: string,
    text: string,
    html: string,
    attachments?: any,
    cc?: any,
  ) {
    if (!this.isConfigured) {
      this.logger.log(`WARNING: Email sending skipped - SendGrid not configured. Would have sent to: ${toEmail}`);
      return { success: false, message: 'SendGrid not configured' };
    }

    try {
      const result = await sgMail.send({
        to: toEmail,
        cc,
        from: {
          name: smtpSenderName,
          email: fromEmail,
        },
        attachments,
        subject,
        text,
        html,
      });

      this.logger.log(`Email sent successfully to: ${toEmail}`);
      return { success: true, result };
    } catch (error) {
      if (error.response) {
        this.logger.log(`ERROR: SendGrid API error: ${JSON.stringify(error.response.body)}`);
      } else {
        this.logger.log(`ERROR: Email sending error: ${error.message}`);
      }
      return { success: false, error: error.message };
    }
  }
}
