import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

interface SecurityEvent {
  timestamp: Date;
  ip: string;
  userAgent: string;
  endpoint: string;
  method: string;
  user?: string;
  eventType: 'AUTH_ATTEMPT' | 'SUSPICIOUS_REQUEST' | 'RATE_LIMIT_HIT' | 'VALIDATION_ERROR';
  details?: any;
}

@Injectable()
export class SecurityAuditMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityAuditMiddleware.name);
  private readonly AUTH_ENDPOINTS = ['/auth', '/login', '/logout', '/register'];
  private readonly ADMIN_ENDPOINTS = ['/admin', '/users', '/system'];

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const ip = this.getClientIp(req);
    const userAgent = req.headers['user-agent'] || 'Unknown';
    const endpoint = req.path;
    const method = req.method;

    // Track authentication attempts
    if (this.isAuthEndpoint(endpoint)) {
      this.logSecurityEvent({
        timestamp: new Date(),
        ip,
        userAgent,
        endpoint,
        method,
        eventType: 'AUTH_ATTEMPT',
        details: { body: this.sanitizeRequestBody(req.body) },
      });
    }

    // Detect suspicious requests
    if (this.isSuspiciousRequest(req)) {
      this.logSecurityEvent({
        timestamp: new Date(),
        ip,
        userAgent,
        endpoint,
        method,
        eventType: 'SUSPICIOUS_REQUEST',
        details: {
          headers: this.sanitizeHeaders(req.headers),
          suspiciousReasons: this.getSuspiciousReasons(req),
        },
      });
    }

    // Track admin endpoint access
    if (this.isAdminEndpoint(endpoint)) {
      this.logger.warn(`Admin endpoint access: ${method} ${endpoint}, IP: ${ip}, UserAgent: ${userAgent}`);
    }

    // Monitor response for security events
    const originalSend = res.send;
    res.send = function (data) {
      const responseTime = Date.now() - startTime;

      // Log slow responses that might indicate attacks
      if (responseTime > 5000) {
        this.logger.warn(`Slow response detected: ${responseTime}ms for ${method} ${endpoint}, IP: ${ip}`);
      }

      return originalSend.call(this, data);
    }.bind(this);

    next();
  }

  private getClientIp(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (req.headers['x-real-ip'] as string) ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'Unknown'
    );
  }

  private isAuthEndpoint(endpoint: string): boolean {
    return this.AUTH_ENDPOINTS.some((authPath) => endpoint.includes(authPath));
  }

  private isAdminEndpoint(endpoint: string): boolean {
    return this.ADMIN_ENDPOINTS.some((adminPath) => endpoint.includes(adminPath));
  }

  private isSuspiciousRequest(req: Request): boolean {
    const userAgent = (req.headers['user-agent'] || '').toLowerCase();
    const suspiciousUserAgents = ['curl', 'wget', 'python', 'scanner', 'bot', 'crawler'];

    // Check for suspicious user agents
    if (suspiciousUserAgents.some((agent) => userAgent.includes(agent))) {
      return true;
    }

    // Check for missing common headers
    if (!req.headers.accept || !req.headers['user-agent']) {
      return true;
    }

    // Check for unusual content types for non-API requests
    const contentType = req.headers['content-type'];
    if (contentType && !this.isValidContentType(contentType)) {
      return true;
    }

    // Check for SQL injection patterns in query parameters
    const queryString = JSON.stringify(req.query).toLowerCase();
    const sqlPatterns = ['union', 'select', 'drop', 'insert', 'delete', 'update', 'script'];
    if (sqlPatterns.some((pattern) => queryString.includes(pattern))) {
      return true;
    }

    return false;
  }

  private getSuspiciousReasons(req: Request): string[] {
    const reasons: string[] = [];
    const userAgent = (req.headers['user-agent'] || '').toLowerCase();

    if (!req.headers.accept) {
      reasons.push('Missing Accept header');
    }
    if (!req.headers['user-agent']) {
      reasons.push('Missing User-Agent header');
    }
    if (userAgent.includes('curl')) {
      reasons.push('Curl user agent');
    }
    if (userAgent.includes('wget')) {
      reasons.push('Wget user agent');
    }
    if (userAgent.includes('python')) {
      reasons.push('Python user agent');
    }

    return reasons;
  }

  private isValidContentType(contentType: string): boolean {
    const validTypes = [
      'application/json',
      'application/x-www-form-urlencoded',
      'multipart/form-data',
      'text/plain',
      'application/xml',
    ];

    return validTypes.some((type) => contentType.toLowerCase().includes(type));
  }

  private sanitizeRequestBody(body: any): any {
    if (!body) {
      return {};
    }

    const sanitized = { ...body };

    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };

    // Remove sensitive headers
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    sensitiveHeaders.forEach((header) => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private logSecurityEvent(event: SecurityEvent): void {
    const logMessage = `Security Event: ${event.eventType} - IP: ${event.ip}, Endpoint: ${event.endpoint}, Method: ${event.method}, UserAgent: ${event.userAgent}`;

    switch (event.eventType) {
      case 'SUSPICIOUS_REQUEST':
        this.logger.warn(`${logMessage}, Reasons: ${event.details?.suspiciousReasons?.join(', ')}`);
        break;
      case 'AUTH_ATTEMPT':
        this.logger.log(`${logMessage}`);
        break;
      default:
        this.logger.log(logMessage);
    }

    // In production, you might want to send this to a security monitoring system
    // this.sendToSecurityMonitoring(event);
  }
}
