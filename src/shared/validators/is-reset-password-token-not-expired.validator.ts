import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { Inject, Injectable } from '@nestjs/common';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Model } from 'mongoose';
import moment = require('moment');

@ValidatorConstraint({ async: true })
@Injectable()
export class IsResetPasswordTokenNotExpiredValidator implements ValidatorConstraintInterface {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
  ) {}

  async validate(token: any, args: ValidationArguments) {
    const userToAttempt = await this.userModel
      .findOne({ resetPasswordToken: token })
      .select('_id requestResetPasswordDate')
      .exec();
    if (!userToAttempt) {
      return true;
    }

    // Hard-coded 48-hour expiration period (172,800 seconds)
    return moment.utc(userToAttempt.requestResetPasswordDate).add(172800, 'seconds') > moment.utc();
  }
}

export function IsResetPasswordTokenNotExpired(validationOptions?: ValidationOptions) {
  return (object: object, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsResetPasswordTokenNotExpiredValidator,
    });
  };
}
