import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { Inject, Injectable } from '@nestjs/common';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Model } from 'mongoose';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsEmailNotExistConstraint implements ValidatorConstraintInterface {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
  ) {}

  async validate(email: any, args: ValidationArguments) {
    return await this.userModel
      .findOne({ email })
      .select('_id')
      .exec()
      .then((id) => !!id);
  }
}

export function IsEmailNotExist(validationOptions?: ValidationOptions) {
  return (object: object, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsEmailNotExistConstraint,
    });
  };
}
