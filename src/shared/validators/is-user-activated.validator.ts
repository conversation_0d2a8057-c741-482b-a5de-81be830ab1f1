import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { Inject, Injectable } from '@nestjs/common';
import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Model } from 'mongoose';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsUserActivatedValidator implements ValidatorConstraintInterface {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
  ) {}

  async validate(email: any) {
    const userToAttempt = await this.userModel.findOne({ email }).select('password').exec();
    if (userToAttempt?.password) {
      return true;
    }
    return false;
  }
}

export function IsUserActivated(validationOptions?: ValidationOptions) {
  return (object: object, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsUserActivatedValidator,
    });
  };
}
