export enum PregnancyMilestoneType {
  BASELINE = 'baseline',
  WEEK_13 = 'week13',
  WEEK_26 = 'week26',
  WEEK_39 = 'week39',
}

export enum PregnancyConstants {
  BASELINE_OFFSET_DAYS = 2,
  WEEK_13_OFFSET_WEEKS = 27, // 40 weeks - 13 weeks
  WEEK_26_OFFSET_WEEKS = 14, // 40 weeks - 26 weeks
  WEEK_39_OFFSET_WEEKS = 1, // 40 weeks - 39 weeks
  DATE_FORMAT = 'DD/MM/YY',
}

export enum PREGNANCY_MILESTONE_DESCRIPTIONS {
  BASELINE = 'Baseline Test (Day 3 after installation)',
  WEEK_13 = 'First Trimester (Week 13 of pregnancy)',
  WEEK_26 = 'Second Trimester (Week 26 of pregnancy)',
  WEEK_39 = 'Third Trimester (Week 39 of pregnancy)',
}
