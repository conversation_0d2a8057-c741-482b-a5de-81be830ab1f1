export enum FlagThreshold {
  H<PERSON>_SINGLE_INCREASE = 7,
  H<PERSON>_SUSTAINED_INCREASE = 5,

  SCCAI_SINGLE_INCREASE = 2,
  SCCAI_SUSTAINED_INCREASE = 2,

  MENT<PERSON>_ANXIETY_THRESHOLD = 11,
  MENTAL_DEPRESSION_THRESHOLD = 11,

  MENTAL_WELLBEING_FLAG_THRESHOLD = 11,
}

export enum OverdueThreshold {
  SYMPTOMS_OVERDUE_DAYS = 14,
  MENTAL_WELLBEING_OVERDUE_DAYS = 42,  // 6 weeks
  GENERAL_WELLBEING_OVERDUE_DAYS = 105, // 15 weeks
}

export enum SymptomsTestSubtype {
  HBI = 'HBI',
  SCCAI = 'SCCAI',
}

export enum FlagType {
  RED_AMBER_RESULT = 'RED_AMBER_RESULT',
  SCORE_INCREASE = 'SCORE_INCREASE',
  SUSTAINED_INCREASE = 'SUSTAINED_INCREASE',
  MENTAL_THRESHOLD = 'MENTAL_THRESHOLD',
  OVERDUE = 'OVERDUE',
}
