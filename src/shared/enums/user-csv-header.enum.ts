export enum UserCsvHeader {
  ID_KEY = 'Id',
  USERNAME_KEY = 'Username',
  PASSWORD_KEY = 'Password',
  CLINICIAN_NAME_KEY = 'Clinician name',
  EMAIL_ADDRESS_KEY = 'Email address',
}

export const ClinicianHeaders = [
  { id: 'username', title: UserCsvHeader.USERNAME_KEY },
  { id: 'password', title: UserCsvHeader.PASSWORD_KEY },
  { id: 'email', title: UserCsvHeader.EMAIL_ADDRESS_KEY },
  { id: 'fullName', title: UserCsvHeader.CLINICIAN_NAME_KEY },
];

export const PatientHeaders = [
  { id: 'username', title: UserCsvHeader.USERNAME_KEY },
  { id: 'password', title: UserCsvHeader.PASSWORD_KEY },
];
