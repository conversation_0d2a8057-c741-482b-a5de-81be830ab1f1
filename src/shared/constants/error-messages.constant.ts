export enum MESSAGES {
  INVALID_CREDENTIALS = 'Invalid username or password. Please try again. If you continue to experience problems, please contact your clinician for help.',
  PATIENT_USERNAME_EXISTS = 'This Patient username already exists, please try another name',
  ERROR_ON_SAVING = 'Error on saving',
  WELCOME_TO_IBD_PRAM = 'Welcome to IBD-PRAM!',
  INVALID_DOWNLOAD_LINK = 'Invalid download link',
  DOWNLOAD_LINK_EXPIRED = 'Download link has expired',
  PATIENT_RESOURCES_LOAD_FAILED = 'Unable to load patient resources. Please check your connection and try again.',
  QUESTIONS_FAILED_TO_LOAD = 'Unable to load questions. Please try loading the test again.',
  MANDATORY_FIELD_INCOMPLETE = 'Please select an answer.',
  CHARACTER_LIMIT_EXCEEDED = 'Results cannot exceed 10 characters.',
  TEXT_FIELD_TOO_LONG = 'Medication descriptions cannot exceed 250 characters.',
  NUMERIC_VALUE_TOO_LONG = 'Health number cannot exceed 3 characters.',
  // Overdue Tests Error Messages
  UNABLE_TO_RETRIEVE_OVERDUE_TESTS = 'Unable to retrieve overdue tests. Please refresh the page or try again later.',
  UNABLE_TO_ACKNOWLEDGE_FLAG = 'Unable to acknowledge this flag. Please try again.',
  UNABLE_TO_SAVE_NOTES = 'Unable to save your note. Please try again.',
  UNABLE_TO_LOAD_TEST_RESULTS = 'Unable to load the test results. Please try again or contact support.',
  NOTES_CANNOT_BE_EDITED = 'Notes cannot be edited once saved.',
  TEST_NOT_OVERDUE = 'Test result is not overdue.',
  INVALID_TEST_RESULT_ID = 'Invalid test result ID.',
  ARCHIVE_USER_REASON_MAX_LENGTH = 'Please enter a valid reason for archiving in 150 characters or less.',
}
