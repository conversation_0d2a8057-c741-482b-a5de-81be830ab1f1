const csv = require('csv-parser');
const fs = require('fs');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

export default class FileHelper {
  static async readCSV(filePath): Promise<any> {
    return new Promise(function (resolve, reject) {
      const fetchData = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          fetchData.push(row);
        })
        .on('end', () => {
          resolve(fetchData);
        })
        .on('error', reject);
    });
  }

  static async exportToCSV(data: any, headers: any) {
    const csvWriter = createCsvWriter(headers);

    await csvWriter.writeRecords(data).then(() => console.log('The CSV file was written successfully'));
  }
}
