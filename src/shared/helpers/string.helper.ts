import { Request } from 'express-serve-static-core';
import * as crypto from 'crypto';
import * as RandExp from 'randexp';

export default class StringHelper {
  /**
   * Generate cryptographically secure random string
   * Replaces Math.random() with crypto.randomBytes() for security
   *
   * @param length Desired length of the random string
   * @returns Cryptographically secure random string using base36 encoding (0-9, a-z)
   *
   * Security Note: This method uses Node.js crypto.randomBytes() which is cryptographically
   * secure, unlike Math.random() which is predictable and unsuitable for security purposes.
   * Uses rejection sampling to avoid modulo bias for uniform distribution.
   */
  static randomString(length: number): string {
    if (!length || length <= 0) {
      throw new Error('Length must be a positive number');
    }

    const charset = '0123456789abcdefghijklmnopqrstuvwxyz'; // 36 characters
    let result = '';

    // Generate random bytes and use rejection sampling to avoid modulo bias
    while (result.length < length) {
      const randomBytes = crypto.randomBytes(length * 2); // Generate extra bytes for efficiency

      for (let i = 0; i < randomBytes.length && result.length < length; i++) {
        const randomValue = randomBytes[i];

        // Use rejection sampling to ensure uniform distribution
        // Only accept values that don't cause bias when using modulo
        // 252 is the largest multiple of 36 that fits in a byte (252 = 36 * 7)
        if (randomValue < 252) {
          const charIndex = randomValue % 36;
          result += charset[charIndex];
        }
        // If randomValue >= 252, reject it and try the next byte
      }
    }

    return result.substring(0, length);
  }

  static getHttpProtocol(request: Request): string {
    return request.protocol == 'https' ? request.protocol : 'http';
  }

  static generateUsername(firstName: string, lastName: string, existingUsernames: string[] = []): string {
    // Remove special characters and spaces, convert to lowercase
    const cleanFirstName = (firstName || '').replace(/[^a-zA-Z]/g, '').toLowerCase();
    const cleanLastName = (lastName || '').replace(/[^a-zA-Z]/g, '').toLowerCase();

    const baseUsername = cleanFirstName + cleanLastName;
    let username = baseUsername;
    let counter = 1;

    // Check if username exists and append number if needed
    while (existingUsernames.includes(username)) {
      username = baseUsername + counter;
      counter++;
    }

    return username;
  }

  static generatePassword(): string {
    // Use RandExp for 8 characters: A-Z and 1-9 only (no 0 or O)
    // Pattern: 8 characters from [A-NP-Z1-9]
    return new RandExp(/^[A-NP-Z]{4}[1-9]{4}$/).gen();
  }
}
