import { Logger } from '@nestjs/common';
import { smtpSenderName, smtpEmail, smtpPass, smtpPort, smtpUrl, smtpUser } from 'app.config';
import * as nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  host: smtpUrl,
  port: smtpPort,
  secure: true,
  pool: true,
  maxConnections: 3,
  maxMessages: 50,
  connectionTimeout: 10000,
  greetingTimeout: 5000,
  socketTimeout: 10000,
  auth: {
    user: smtpUser,
    pass: smtpPass,
  },
});

export async function sendMail(email: any, subject: string, html: string) {
  const mailInfo = {
    from: `${smtpSenderName} ${smtpEmail}`, // sender address
    to: email, // list of receivers
    subject, // Subject line
    text: html, // plain text body
    html, // html body
  };
  try {
    const resultSendMail = await transporter.sendMail(mailInfo);
    Logger.log(resultSendMail, 'Send mail');
  } catch (error) {
    Logger.error(error, null, 'Send mail error');
  }
}
