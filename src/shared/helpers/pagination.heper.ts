import { PaginationQueryParam, PaginationRO } from './../interfaces/pagination.interface';
export default class PaginationHelper {
  static buildPaginationQueryParams(page, limit): PaginationQueryParam {
    return {
      limit: parseInt(limit),
      page: parseInt(page),
      skip: (parseInt(page) - 1) * parseInt(limit),
    };
  }

  static buildPaginationRO(page, limit, total): PaginationRO {
    return {
      totalDocs: parseInt(total),
      totalPages: Math.ceil(total / limit),
      limit: parseInt(limit),
      page: parseInt(page),
    };
  }
}
