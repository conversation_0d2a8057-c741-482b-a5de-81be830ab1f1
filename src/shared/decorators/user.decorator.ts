import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';

import { jwtSecret } from '../../app.config';

export const User = createParamDecorator((data: string | undefined, ctx: ExecutionContext) => {
  // Retrieve the underlying request object from the current execution context
  const req = ctx.switchToHttp().getRequest();

  // If authentication middleware has already attached the user object, use it directly
  if (req?.user) {
    return data ? req.user[data] : req.user;
  }

  // Fallback: try to decode the JWT manually from the Authorization header
  const authHeader: string | undefined = req?.headers?.authorization;
  const tokenParts = authHeader ? authHeader.split(' ') : null; // Expecting format: "Bearer <token>"

  if (tokenParts && tokenParts.length === 2) {
    const decoded: any = jwt.verify(tokenParts[1], jwtSecret);
    return data ? decoded[data] : decoded.user;
  }

  return null;
});
