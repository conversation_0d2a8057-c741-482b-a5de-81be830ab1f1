import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { FeaturesModule } from './features/features.module';
import { LoggerModule } from './logger/logger.module';
import { EmailService } from './shared/services/email.service';
import { FirebaseService } from './shared/services/firebase.service';
import { MailerService } from './shared/services/mailer.service';
import { SharedModule } from './shared/shared.module';

@Module({
  imports: [DatabaseModule, FeaturesModule, SharedModule, LoggerModule.forRoot(), ScheduleModule.forRoot()],
  controllers: [AppController],
  providers: [AppService, MailerService, FirebaseService, EmailService],
})
export class AppModule {}
