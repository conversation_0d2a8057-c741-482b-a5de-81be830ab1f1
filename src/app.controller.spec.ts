import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(() => {
    // Create a mock AppService
    appService = {
      getHello: jest.fn().mockReturnValue('Hello World!!!'),
    } as any;

    // Create controller with mock service
    appController = new AppController(appService);
  });

  describe('root', () => {
    it('should return "Hello World!!!"', () => {
      expect(appController.getHello()).toBe('Hello World!!!');
      expect(appService.getHello).toHaveBeenCalled();
    });
  });
});
