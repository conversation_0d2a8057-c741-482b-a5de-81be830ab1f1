import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { PatientResourceModule } from './patient-resources/patient-resource.module';
import { PatientModule } from './patient/patient.module';
import { SeedModule } from './seed/seed.module';
import { TestResultModule } from './test-results/test-result.module';
import { TestModule } from './tests/test.module';
import { UserModule } from './user/user.module';

@Module({
  imports: [AuthModule, PatientResourceModule, TestModule, TestResultModule, UserModule, PatientModule, SeedModule],
  exports: [AuthModule, PatientResourceModule, TestModule, TestResultModule, UserModule, PatientModule, SeedModule],
})
export class FeaturesModule {}
