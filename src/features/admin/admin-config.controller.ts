import { Permissions } from '@aos-decorator/permission.decorator';
import { User } from '@aos-decorator/user.decorator';
import { UserRole } from '@aos-enum/user-role.enum';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { PermissionGuard } from '@aos-shared/guards/permission.guard';
import { AppConfigService } from '@aos-shared/services/app-config.service';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Logger,
  Put,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UpdateRedFlagEmailDto } from './dto/update-red-flag-email.dto';

@ApiBearerAuth()
@ApiTags('admin')
@Controller('admin/config')
@UseGuards(AuthGuard, PermissionGuard)
export class AdminConfigController {
  private readonly logger = new Logger(AdminConfigController.name);

  constructor(private readonly appConfigService: AppConfigService) {}

  @ApiOperation({ description: 'Get red flag email configuration' })
  @ApiResponse({
    status: 200,
    description: 'Red flag email configuration retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Super Admin access required' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @Get('red-flag-email')
  @Permissions(UserRole.SUPER_ADMIN)
  async getRedFlagEmail(): Promise<{ email: string }> {
    try {
      const email = await this.appConfigService.getRedFlagEmail();
      this.logger.log(`Red flag email configuration retrieved successfully`);
      return { email };
    } catch (error) {
      this.logger.error(`Failed to retrieve red flag email configuration: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve red flag email configuration');
    }
  }

  @ApiOperation({ description: 'Update red flag email configuration' })
  @ApiResponse({
    status: 200,
    description: 'Red flag email configuration updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid email format' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Super Admin access required' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @Put('red-flag-email')
  @Permissions(UserRole.SUPER_ADMIN)
  async updateRedFlagEmail(
    @Body() dto: UpdateRedFlagEmailDto,
    @User() user: any,
  ): Promise<{ success: boolean }> {
    try {
      if (!user || !user._id) {
        this.logger.warn('Update red flag email attempted without valid user session');
        throw new UnauthorizedException('Valid user session required');
      }

      await this.appConfigService.setRedFlagEmail(dto.email, user._id);
      
      this.logger.log(`Red flag email configuration updated successfully by user ${user._id} to ${dto.email}`);
      return { success: true };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error(`Failed to update red flag email configuration: ${error.message}`, error.stack);
      
      if (error.message.includes('Invalid email format')) {
        throw new BadRequestException('Invalid email format provided');
      }
      
      throw new InternalServerErrorException('Failed to update red flag email configuration');
    }
  }
}
