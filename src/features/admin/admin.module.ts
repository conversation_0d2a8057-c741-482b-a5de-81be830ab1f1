import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { DatabaseModule } from '@aos-database/database.module';
import { SharedModule } from '@aos-shared/shared.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { AdminConfigController } from './admin-config.controller';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
  ],
  controllers: [AdminConfigController],
  providers: [],
  exports: [],
})
export class AdminModule {}
