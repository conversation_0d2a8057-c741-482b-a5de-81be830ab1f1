export interface ImportAnswerData {
  text: string;
  questions: any[];
  code: string;
  additionalNote?: string;
  rag?: string;
  score?: number;
  anxietyScore?: number;
  depressionScore?: number;
  sibdqScore?: number;
  eqScore?: number;
}

export interface ImportQuestionData {
  text: string;
  type: string;
  code: string;
  order: number | string;
  questions: any[];
  answers: ImportAnswerData[];
  testDate?: string | null;
  textValue?: string | null;
  numericValue?: number | null;
  mandatory?: boolean;
}
