import { DatabaseModule } from '@aos-database/database.module';
import { Module } from '@nestjs/common';
import { MailerService } from '../../shared/services/mailer.service';
import { SharedModule } from '../../shared/shared.module';
import { PregnancyMilestoneService } from '../tests/pregnancy-milestone.service';
import { LoggerModule } from './../../logger/logger.module';
import { GenerateImportUserFilesService } from './generate-import-users-file.service';
import { ImportDataService } from './import-data.service';
import { SeedConfigService } from './seed-config.service';
import { Seeder } from './seed.service';

@Module({
  imports: [DatabaseModule, LoggerModule.forRoot(), SharedModule],
  providers: [
    Seeder,
    SeedConfigService,
    ImportDataService,
    GenerateImportUserFilesService,
    PregnancyMilestoneService,
    MailerService,
  ],
  controllers: [],
  exports: [Seeder, ImportDataService],
})
export class SeedModule {}
