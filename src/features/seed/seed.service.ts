import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { UserRole } from '@aos-enum/user-role.enum';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import StringHelper from 'shared/helpers/string.helper';
import { ImportDataService } from './import-data.service';
import { SeedConfigService } from './seed-config.service';

@Injectable()
export class Seeder {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Test))
    private readonly testModel: Model<any>,
    private readonly importDataService: ImportDataService,
    private readonly seedConfigService: SeedConfigService,
  ) { }

  async importDataDefault() {
    console.log('🔍 Checking if default data exists...');

    const [demoCount, testCount] = await Promise.all([
      this.userModel.countDocuments({ role: UserRole.DEMO }),
      this.testModel.countDocuments()
    ]);

    if (demoCount === 0 || testCount === 0) {
      console.log('📦 Missing default data, seeding...');
      console.log(`  - Demo accounts: ${demoCount > 0 ? '✅' : '❌'}`);
      console.log(`  - Test data: ${testCount > 0 ? '✅' : '❌'}`);

      await Promise.all([
        demoCount === 0 ? this.importDemoAccounts() : Promise.resolve(),
        testCount === 0 ? this.importDataService.importData() : Promise.resolve()
      ]);

      console.log('✅ Default data seeding completed!');
      if (demoCount === 0) {
        console.log('🎭 Run "npm run cli list-demo" to see demo passwords');
      }
    } else {
      console.log('✅ Default data already exists, skipping seed');
    }
  }

  async seedAll() {
    console.log('🌱 Starting data seeding...');

    // Log security configuration
    this.seedConfigService.logSecurityInfo();

    // Track credentials for file output
    const seedCredentials: Array<{username: string, password: string, role: string}> = [];

    await Promise.all([
      this.importUserAdmin(),
      this.importSampleClinicians(seedCredentials),
      this.importSamplePatients(seedCredentials),
      this.importDemoAccounts(),
    ]);

    // Import tests after users
    try {
      await this.importDataService.importData();
      console.log('✅ Test data imported successfully!');
    } catch (error) {
      console.log('⚠️  Test data import failed:', error.message);
    }

    // Save credentials to file for development convenience
    if (seedCredentials.length > 0) {
      this.seedConfigService.saveSeedCredentials(seedCredentials);
    }

    console.log('✅ All data seeded successfully!');
    console.log('📋 Sample accounts created:');
    console.log('👨‍⚕️ Clinicians: clinician1, clinician2 (passwords logged during creation)');
    console.log('👥 Patients: patient_crohns, patient_uc, patient_no_subtype (passwords logged during creation)');
    console.log('🎭 Demo Accounts:');
    console.log('  - Run "npm run cli list-demo" to see passwords');
  }

  async seedUsers() {
    console.log('👥 Seeding users...');

    // Track credentials for file output
    const seedCredentials: Array<{username: string, password: string, role: string}> = [];

    await Promise.all([this.importUserAdmin(), this.importSampleClinicians(seedCredentials), this.importSamplePatients(seedCredentials)]);

    // Save credentials to file for development convenience
    if (seedCredentials.length > 0) {
      this.seedConfigService.saveSeedCredentials(seedCredentials);
    }

    console.log('✅ Users seeded successfully!');
  }

  private async importUserAdmin() {
    const usersRaw = [
      {
        username: 'andysmallwood',
        email: '<EMAIL>',
        fullname: 'Andy Smallwood',
      },
      {
        username: 'samirmehta',
        email: '<EMAIL>',
        fullname: 'Samir Mehta',
      },
      {
        username: 'clivestubbs',
        email: '<EMAIL>',
        fullname: 'Clive Stubbs',
      },
      {
        username: 'oliviamcleod',
        email: '<EMAIL>',
        fullname: 'Olivia McLeod',
      },
      {
        username: 'nazishkhan',
        email: '<EMAIL>',
        fullname: 'Nazish Khan',
      },
      {
        username: 'jamescotton',
        email: '<EMAIL>',
        fullname: 'James Cotton',
      },
    ];

    for (const userRaw of usersRaw) {
      const user = await this.userModel.findOne({ username: userRaw.username });

      if (user) {
        continue;
      }

      const userCreate = new this.userModel({
        username: userRaw.username,
        email: userRaw.email,
        fullname: userRaw.fullname,
        firstName: userRaw.fullname.split(' ')[0],
        lastName: userRaw.fullname.split(' ')[1],
        password: StringHelper.randomString(6),
        role: UserRole.SUPER_ADMIN,
      });

      await userCreate.save();
    }
  }

  private async importSampleClinicians(credentialsArray?: Array<{username: string, password: string, role: string}>) {
    const clinicians = [
      {
        username: 'clinician1',
        email: '<EMAIL>',
        fullname: 'Dr. John Smith',
      },
      {
        username: 'clinician2',
        email: '<EMAIL>',
        fullname: 'Dr. Sarah Johnson',
      },
    ];

    for (const clinicianRaw of clinicians) {
      const existing = await this.userModel.findOne({
        username: clinicianRaw.username,
      });
      if (existing) {
        continue;
      }

      const password = this.seedConfigService.getSeedPassword('clinician');

      const clinician = new this.userModel({
        username: clinicianRaw.username,
        email: clinicianRaw.email,
        fullname: clinicianRaw.fullname,
        firstName: clinicianRaw.fullname.split(' ')[1],
        lastName: clinicianRaw.fullname.split(' ')[2],
        password: password,
        role: UserRole.CLINICIAN,
      });

      await clinician.save();
      console.log(`Created clinician: ${clinicianRaw.username} with password: ${password}`);

      // Add to credentials array for file output
      if (credentialsArray) {
        credentialsArray.push({
          username: clinicianRaw.username,
          password: password,
          role: 'CLINICIAN'
        });
      }
    }
  }

  private async importSamplePatients(credentialsArray?: Array<{username: string, password: string, role: string}>) {
    const patients = [
      {
        username: 'patient_crohns',
        email: '<EMAIL>',
        fullname: 'Alice Brown',
        ibdSubtype: IBDSubtype.CROHNS_DISEASE,
        description: "Patient with Crohn's Disease",
      },
      {
        username: 'patient_uc',
        email: '<EMAIL>',
        fullname: 'Bob Wilson',
        ibdSubtype: IBDSubtype.ULCERATIVE_COLITIS,
        description: 'Patient with Ulcerative Colitis',
      },
      {
        username: 'patient_no_subtype',
        email: '<EMAIL>',
        fullname: 'Charlie Davis',
        description: 'Patient without IBD subtype (for testing error cases)',
      },
    ];

    for (const patientRaw of patients) {
      const existing = await this.userModel.findOne({
        username: patientRaw.username,
      });
      if (existing) {
        continue;
      }

      const password = this.seedConfigService.getSeedPassword('patient');

      const patientData: any = {
        username: patientRaw.username,
        email: patientRaw.email,
        fullname: patientRaw.fullname,
        firstName: patientRaw.fullname.split(' ')[0],
        lastName: patientRaw.fullname.split(' ')[1],
        password: password,
        role: UserRole.PATIENT,
        isFirstTimeLogin: true,
      };

      if (patientRaw.ibdSubtype) {
        patientData.ibdSubtype = patientRaw.ibdSubtype;
      }

      const patient = new this.userModel(patientData);

      await patient.save();
      console.log(`Created patient: ${patientRaw.username} with password: ${password}`);

      // Add to credentials array for file output
      if (credentialsArray) {
        credentialsArray.push({
          username: patientRaw.username,
          password: password,
          role: 'PATIENT'
        });
      }
    }
  }

  private async importDemoAccounts() {
    const demoAccounts = [
      {
        username: 'demoHBI1',
        firstName: 'Demonstration',
        lastName: 'Account',
        ibdSubtype: IBDSubtype.CROHNS_DISEASE,
        description: "Demo account for HBI test (Crohn's Disease)",
      },
      {
        username: 'demoHBI2',
        firstName: 'Demonstration',
        lastName: 'Account',
        ibdSubtype: IBDSubtype.CROHNS_DISEASE,
        description: "Demo account for HBI test (Crohn's Disease)",
      },
      {
        username: 'demoSCCAI1',
        firstName: 'Demonstration',
        lastName: 'Account',
        ibdSubtype: IBDSubtype.ULCERATIVE_COLITIS,
        description: 'Demo account for SCCAI test (Ulcerative Colitis)',
      },
      {
        username: 'demoSCCAI2',
        firstName: 'Demonstration',
        lastName: 'Account',
        ibdSubtype: IBDSubtype.ULCERATIVE_COLITIS,
        description: 'Demo account for SCCAI test (Ulcerative Colitis)',
      },
    ];

    for (const demoAccountRaw of demoAccounts) {
      const existing = await this.userModel.findOne({
        username: demoAccountRaw.username,
      });
      if (existing) {
        continue;
      }

      const password = StringHelper.generatePassword();

      const demoAccount = new this.userModel({
        username: demoAccountRaw.username,
        firstName: demoAccountRaw.firstName,
        lastName: demoAccountRaw.lastName,
        fullName: `${demoAccountRaw.firstName} ${demoAccountRaw.lastName}`,
        password,
        revealPassword: password,
        role: UserRole.DEMO,
        ibdSubtype: demoAccountRaw.ibdSubtype,
        sessionIBDSubtype: demoAccountRaw.ibdSubtype,
        nhsNumber: 'N/A',
        isFirstTimeLogin: false,
      });

      await demoAccount.save();
      console.log(`Created demo account: ${demoAccountRaw.username} with password: ${password}`);
    }
  }

  async seedDemoAccounts() {
    console.log('🎭 Creating demo accounts...');

    // Remove existing demo accounts
    await this.userModel.deleteMany({ role: UserRole.DEMO });

    await this.importDemoAccounts();

    console.log('✅ Demo accounts created successfully!');
  }

  async listDemoAccounts() {
    const demoAccounts = await this.userModel.find({ role: UserRole.DEMO });

    console.log('🎭 Demo Accounts:');
    demoAccounts.forEach((account) => {
      console.log(`  - ${account.username}: ${account.revealPassword} (${account.ibdSubtype})`);
    });
  }
}
