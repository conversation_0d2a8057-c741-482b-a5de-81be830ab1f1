import { Injectable, Logger } from '@nestjs/common';
import { NodeEnv } from 'shared/enums/node-env.enum';
import StringHelper from 'shared/helpers/string.helper';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

export interface SeedPasswordConfig {
  clinicianPassword: string;
  patientPassword: string;
  useRandomPasswords: boolean;
  environment: string;
}

@Injectable()
export class SeedConfigService {
  private readonly logger = new Logger(SeedConfigService.name);

  /**
   * Get secure password configuration for seed data
   * Prioritizes security while maintaining development convenience
   */
  getSeedPasswordConfig(): SeedPasswordConfig {
    const nodeEnv = process.env.NODE_ENV;
    const isProduction = nodeEnv === NodeEnv.PRODUCTION;
    const isUAT = nodeEnv === NodeEnv.UAT;

    // In production and UAT, always use random passwords for security
    if (isProduction || isUAT) {
      this.logger.log('Using random passwords for seed data (production/UAT environment)');
      return {
        clinicianPassword: StringHelper.generatePassword(),
        patientPassword: StringHelper.generatePassword(),
        useRandomPasswords: true,
        environment: nodeEnv || 'unknown',
      };
    }

    // In development/test environments, check for environment variables
    const envClinicianPassword = process.env.SEED_CLINICIAN_PASSWORD;
    const envPatientPassword = process.env.SEED_PATIENT_PASSWORD;

    // If environment variables are provided, use them (for development convenience)
    if (envClinicianPassword && envPatientPassword) {
      this.logger.warn('Using environment-configured passwords for seed data (development only)');
      return {
        clinicianPassword: envClinicianPassword,
        patientPassword: envPatientPassword,
        useRandomPasswords: false,
        environment: nodeEnv || 'development',
      };
    }

    // Default: generate random passwords even in development for better security
    this.logger.log('Using random passwords for seed data (no environment passwords configured)');
    return {
      clinicianPassword: StringHelper.generatePassword(),
      patientPassword: StringHelper.generatePassword(),
      useRandomPasswords: true,
      environment: nodeEnv || 'development',
    };
  }

  /**
   * Get password for specific account type
   */
  getSeedPassword(accountType: 'clinician' | 'patient'): string {
    const config = this.getSeedPasswordConfig();
    return accountType === 'clinician' ? config.clinicianPassword : config.patientPassword;
  }

  /**
   * Check if current environment allows predictable passwords
   */
  isSecureEnvironment(): boolean {
    const nodeEnv = process.env.NODE_ENV;
    return nodeEnv === NodeEnv.PRODUCTION || nodeEnv === NodeEnv.UAT;
  }

  /**
   * Log security information about seed password configuration
   */
  logSecurityInfo(): void {
    const config = this.getSeedPasswordConfig();

    this.logger.log(`Seed password configuration:`);
    this.logger.log(`  Environment: ${config.environment}`);
    this.logger.log(`  Using random passwords: ${config.useRandomPasswords}`);

    if (!config.useRandomPasswords && !this.isSecureEnvironment()) {
      this.logger.warn('⚠️  Using predictable passwords in development environment');
      this.logger.warn('⚠️  This is acceptable for development but should never be used in production');
    }
  }

  /**
   * Save seed account credentials to a temporary file for development convenience
   * Only works in non-production environments
   */
  saveSeedCredentials(credentials: Array<{username: string, password: string, role: string}>): void {
    if (this.isSecureEnvironment()) {
      this.logger.warn('Seed credentials file creation skipped in secure environment');
      return;
    }

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `seed-credentials-${timestamp}.json`;
      const filepath = join(process.cwd(), 'temp', filename);

      // Ensure temp directory exists
      const tempDir = dirname(filepath);
      if (!existsSync(tempDir)) {
        mkdirSync(tempDir, { recursive: true });
      }

      const credentialsData = {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        warning: 'This file contains sensitive information. Delete after use.',
        credentials: credentials
      };

      writeFileSync(filepath, JSON.stringify(credentialsData, null, 2));
      this.logger.log(`💾 Seed credentials saved to: ${filepath}`);
      this.logger.warn('⚠️  Remember to delete this file after use!');
    } catch (error) {
      this.logger.error('Failed to save seed credentials file:', error.message);
    }
  }
}
