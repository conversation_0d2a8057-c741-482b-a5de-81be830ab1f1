import { UserCsvHeader } from '@aos-enum/user-csv-header.enum';
import { UserRole } from '@aos-enum/user-role.enum';
import { Injectable } from '@nestjs/common';
import FileHelper from 'shared/helpers/file.helper';
import StringHelper from 'shared/helpers/string.helper';
import { seeder } from '../../app.config';
import { ClinicianHeaders, PatientHeaders } from '../../shared/enums/user-csv-header.enum';

@Injectable()
export class GenerateImportUserFilesService {
  async generateFiles(): Promise<void> {
    await this.importUser(UserRole.PATIENT, seeder.patientImportPath);
  }

  private async importUser(userRole: UserRole, filePath: string) {
    const rows = await FileHelper.readCSV(filePath);
    const data = [];

    for (const row of rows) {
      const password = userRole === UserRole.CLINICIAN ? StringHelper.randomString(6) : StringHelper.randomString(4);

      if (userRole === UserRole.CLINICIAN) {
        data.push({
          fullName: userRole === UserRole.CLINICIAN ? row[UserCsvHeader.CLINICIAN_NAME_KEY] : null,
          username: row[UserCsvHeader.USERNAME_KEY],
          email: row[UserCsvHeader.EMAIL_ADDRESS_KEY] ? row[UserCsvHeader.EMAIL_ADDRESS_KEY] : null,
          password,
        });
      } else {
        data.push({
          username: row[UserCsvHeader.USERNAME_KEY],
          password,
        });
      }
    }

    await FileHelper.exportToCSV(data, {
      path: userRole === UserRole.CLINICIAN ? seeder.clinicianExportPath : seeder.patientExportPath,
      header: userRole === UserRole.CLINICIAN ? ClinicianHeaders : PatientHeaders,
    });
  }
}
