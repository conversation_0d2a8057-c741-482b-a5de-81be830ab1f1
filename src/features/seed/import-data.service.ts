import {
  AosCollections,
  getCollectionToken,
} from '@aos-database/database.constant';
import { UserRole } from '@aos-enum/user-role.enum';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { seeder } from '../../app.config';
import { IBDSubtype } from '../../shared/enums/ibd-subtype.enum';
import { TestSubtype } from '../../shared/enums/test-subtype.enum';
import { TestType } from '../../shared/enums/test-type.enum';
import { ImportAnswerData, ImportQuestionData } from './interface/import-data.interface';
const fs = require('fs');

interface TestConfiguration {
  type: TestType;
  intervalDays?: number;
  firstTimeIntervalDays: number;
  ibdSubtype?: IBDSubtype;
}

@Injectable()
export class ImportDataService {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.PatientResource))
    private readonly patientResourceModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Test))
    private readonly testModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Question))
    private readonly questionModel: Model<any>,
  ) { }

  async importData(): Promise<void> {
    await this.importPatientResource(seeder.patientResourcePath);
    await this.importTestData(seeder.testDataPath);
  }

  private async importPatientResource(filePath: string) {
    const rawdata = fs.readFileSync(filePath);
    const rows = JSON.parse(rawdata);
    await this.patientResourceModel.deleteMany();

    for (const row of rows) {
      const resource = new this.patientResourceModel(row);
      await resource.save();
    }
  }

  private async importTestData(filePath: string) {
    const rawdata = fs.readFileSync(filePath);
    const records = JSON.parse(rawdata);

    await this.clearExistingTestData();

    for (const record of records) {
      const testConfig = this.getTestConfiguration(record.title);
      const questions = await this.importQuestions(record.questions);
      const testData = this.buildTestData(record, testConfig, questions);
      const newTest = new this.testModel(testData);
      await newTest.save();
    }
  }

  private async clearExistingTestData(): Promise<void> {
    await Promise.all([
      this.testModel.deleteMany(),
      this.questionModel.deleteMany(),
      this.testResultModel.deleteMany(),
      this.userModel.updateMany(
        { role: UserRole.PATIENT },
        { $set: { isFirstTimeLogin: true } },
      ),
    ]);
  }

  private getTestConfiguration(title: string): TestConfiguration {
    const configurations: Record<string, TestConfiguration> = {
      [TestSubtype.HBI]: {
        type: TestType.SYMPTOMS,
        intervalDays: 7,
        firstTimeIntervalDays: 0,
        ibdSubtype: IBDSubtype.CROHNS_DISEASE,
      },
      [TestSubtype.SCCAI]: {
        type: TestType.SYMPTOMS,
        intervalDays: 7,
        firstTimeIntervalDays: 0,
        ibdSubtype: IBDSubtype.ULCERATIVE_COLITIS,
      },
      [TestSubtype.MENTAL_WELLBEING]: {
        type: TestType.MENTAL_WELLBEING,
        intervalDays: 28,
        firstTimeIntervalDays: 1,
      },
      [TestSubtype.GENERAL_WELLBEING]: {
        type: TestType.GENERAL_WELLBEING,
        firstTimeIntervalDays: 2,
      },
    };

    return configurations[title] || {
      type: TestType.SYMPTOMS,
      intervalDays: 7,
      firstTimeIntervalDays: 0,
    };
  }

  private buildTestData(record: any, config: TestConfiguration, questions: any[]): any {
    const testData: any = {
      title: record.title,
      disclaimer: record.disclaimer,
      instructions: record.instructions,
      firstTimeIntervalDays: config.firstTimeIntervalDays,
      type: config.type,
      questions,
    };

    if (config.intervalDays) {
      testData.intervalDays = config.intervalDays;
    }

    if (config.ibdSubtype) {
      testData.ibdSubtype = config.ibdSubtype;
    }

    return testData;
  }

  private async importQuestions(questions: any[]): Promise<any[]> {
    if (!questions) {
      return [];
    }

    const objectIds = [];

    for (const questionObj of questions) {
      const processedQuestion = await this.processQuestion(questionObj);
      objectIds.push(processedQuestion);
    }

    return objectIds;
  }

  private async processQuestion(questionObj: any): Promise<any> {
    const subQuestionIds = await this.importQuestions(questionObj.questions);
    const answers = await this.processAnswers(questionObj.answers);

    const questionData: ImportQuestionData = {
      text: questionObj.text,
      type: questionObj.type,
      code: questionObj.code || '',
      order: questionObj.order || '',
      questions: subQuestionIds,
      answers,
    };

    if (questionObj?.testDate !== undefined && questionObj?.testDate !== null) {
      questionData.testDate = questionObj.testDate;
    }

    if (questionObj?.mandatory !== undefined) {
      questionData.mandatory = questionObj.mandatory;
    }

    if (questionObj?.textValue !== undefined && questionObj?.textValue !== null) {
      questionData.textValue = questionObj.textValue;
    }

    if (questionObj?.numericValue !== undefined) {
      questionData.numericValue = questionObj.numericValue;
    }

    const newQuestion = new this.questionModel(questionData);

    await newQuestion.save();
    return newQuestion;
  }

  private async processAnswers(answers: any[]): Promise<ImportAnswerData[]> {
    if (!answers) {
      return [];
    }

    const processedAnswers = [];

    for (const answerObj of answers) {
      const answerData = await this.createAnswerData(answerObj);
      processedAnswers.push(answerData);
    }

    return processedAnswers;
  }

  private async createAnswerData(answerObj: any): Promise<ImportAnswerData> {
    const answerData: ImportAnswerData = {
      text: answerObj.text,
      questions: await this.importQuestions(answerObj.questions),
      code: answerObj.code || '',
      additionalNote: answerObj.additionalNote,
      rag: answerObj.rag,
      score: answerObj.score,
    };

    this.addOptionalScores(answerData, answerObj);
    return answerData;
  }

  private addOptionalScores(answerData: ImportAnswerData, answerObj: any): void {
    const scoreFields = ['anxietyScore', 'depressionScore', 'sibdqScore', 'eqScore'];

    scoreFields.forEach(field => {
      if (answerObj[field] != null) {
        answerData[field] = answerObj[field];
      }
    });
  }
}
