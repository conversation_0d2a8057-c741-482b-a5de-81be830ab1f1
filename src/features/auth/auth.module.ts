import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LoggerModule } from './../../logger/logger.module';
import { FirebaseService } from './../../shared/services/firebase.service';

import { DatabaseModule } from '@aos-database/database.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { SharedModule } from '../../shared/shared.module';
import { CalculatorService } from '../test-results/calculator.service';
import { TestResultService } from '../test-results/test-result.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
    LoggerModule.forRoot(),
  ],
  providers: [AuthService, JwtStrategy, FirebaseService, TestResultService, CalculatorService],
  controllers: [AuthController],
  exports: [PassportModule],
})
export class AuthModule {}
