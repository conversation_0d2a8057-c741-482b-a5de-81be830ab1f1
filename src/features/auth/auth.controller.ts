import { getCollectionToken } from '@aos-database/database.constant';
import { User } from '@aos-decorator/user.decorator';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { Body, Controller, Get, Inject, Logger, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Model } from 'mongoose';
import { TestResultService } from '../test-results/test-result.service';
import { TestService } from '../tests/test.service';
import { AosCollections } from './../../database/database.constant';
import { TestResultStatus } from './../../shared/enums/test-result-status.enum';
import { PermissionGuard } from './../../shared/guards/permission.guard';
import { FirebaseService } from './../../shared/services/firebase.service';
import { AuthService } from './auth.service';
import { RegisterUserDto, RequestTokenDto } from './dto';
import { LogoutDto } from './dto/logout.dto';
import { RequestAccessTokenDto } from './dto/request-access-token.dto';
import { ValidateRO, ValidationUserRO } from './interface/validate.interface';

@Controller()
@ApiTags('authentication')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);
  constructor(
    private readonly authService: AuthService,
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    private readonly testService: TestService,
    private readonly testResultService: TestResultService,
    private readonly firebaseService: FirebaseService,
  ) {}

  @ApiOperation({ description: ' Sign In ' })
  @Post('auth/login')
  async requestToken(@Body() requestTokenDto: RequestTokenDto): Promise<ValidateRO> {
    return this.authService.validateUserByPassword(requestTokenDto);
  }

  @ApiOperation({ description: ' Get User by token ' })
  @ApiBearerAuth()
  @Get('auth/info')
  @UseGuards(AuthGuard, PermissionGuard)
  async getValidatedUser(@User('_id') userId): Promise<ValidationUserRO> {
    return this.authService.getValidatedUserById(userId);
  }

  @ApiOperation({ description: ' Sign Up ' })
  @Post('auth/register')
  async createUser(@Body() createUserDto: RegisterUserDto): Promise<ValidateRO> {
    return await this.authService.register(createUserDto);
  }

  @ApiOperation({ description: ' Logout ' })
  @Post('auth/logout')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  async logout(@Body() dto: LogoutDto, @User() loggedUser): Promise<void> {
    return await this.authService.logout(dto, loggedUser);
  }

  @ApiOperation({ description: ' Get Refresh Token ' })
  @Post('auth/refresh-token')
  async requestNewToken(@Body() requestAccessTokenDto: RequestAccessTokenDto): Promise<ValidateRO> {
    return this.authService.requestNewAccessToken(requestAccessTokenDto);
  }
}
