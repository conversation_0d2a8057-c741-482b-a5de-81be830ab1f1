import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { UserRole } from '@aos-enum/user-role.enum';

export interface ValidationUserRO {
  username: string;
  role: UserRole;
  isFirstTimeLogin: boolean;
  firstName: string;
  lastName: string;
  isArchived: boolean;
  ibdSubtype?: IBDSubtype;
}

export interface ValidateRO {
  token: string;
  refreshToken: string;
  expiredIn: number;
  user: ValidationUserRO;
}
