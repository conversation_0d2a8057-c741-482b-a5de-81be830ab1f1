import { IsEmailAlreadyExist } from '@aos-validator/is-email-already-exist.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, Matches, MaxLength, MinLength } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty()
  @IsEmail()
  @IsEmailAlreadyExist({
    message: 'Email $value already exists. Choose another name.',
  })
  readonly email: string;

  @ApiProperty()
  @Matches(/^[a-zA-Z0-9]+$/, {
    message: 'Password contains only digit and word',
  })
  @MinLength(8, {
    message: 'Min length is 8 characters',
  })
  readonly password: string;

  @ApiProperty()
  @MinLength(3, {
    message: 'Minimum length is 3 characters',
  })
  @MaxLength(50, {
    message: 'Maximum length is 50 characters',
  })
  @Matches(/^[a-z ]+$/i, {
    message: 'Full name contains only text',
  })
  readonly fullName: string;
}
