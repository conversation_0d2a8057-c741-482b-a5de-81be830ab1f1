import { IsResetPasswordTokenExist } from '@aos-validator/is-reset-password-token-exist.validator';
import { IsResetPasswordTokenNotExpired } from '@aos-validator/is-reset-password-token-not-expired.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, Matches, MinLength } from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty()
  @Matches(/^[a-zA-Z0-9]+$/, {
    message: 'Password contains only digit and word',
  })
  @MinLength(8, {
    message: 'Min length is 8 characters',
  })
  readonly password: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsResetPasswordTokenExist({
    message: 'Token $value does not exist. Please try again.',
  })
  @IsResetPasswordTokenNotExpired({
    message: 'Sorry, this link has expired.',
  })
  readonly token: string;
}
