import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MaxLength } from 'class-validator';

export class RequestTokenDto {
  @ApiProperty({
    description: 'Username',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'This field is required.' })
  @MaxLength(100, { message: 'Username cannot exceed 100 characters.' })
  readonly username: string;

  @ApiProperty({
    description: 'Password',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'This field is required.' })
  @MaxLength(100, { message: 'Password cannot exceed 100 characters.' })
  @Matches(/^[a-zA-Z0-9]+$/, {
    message: 'Password contains only digit and word',
  })
  readonly password: string;
}
