import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { TestService } from '../tests/test.service';

import {
  AosCollections,
  getCollectionToken,
} from '@aos-database/database.constant';
import { UserRole } from '@aos-enum/user-role.enum';
import { MESSAGES } from '@aos-shared/constants/error-messages.constant';
import {
  jwtRefreshSecret,
  jwtSecret,
  refreshTokenExpired,
  tokenExpired,
} from '../../app.config';
import { RegisterUserDto, RequestTokenDto } from './dto';
import { LogoutDto } from './dto/logout.dto';
import { RequestAccessTokenDto } from './dto/request-access-token.dto';
import { JwtPayload } from './interface/jwt-payload.interface';
import { RequestUserAttachmentInterface } from './interface/request-user-attachment.interface';
import { ValidateRO, ValidationUserRO } from './interface/validate.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    private readonly jwtService: JwtService,
    private readonly testService: TestService,
  ) {}

  async validateUserByPassword(
    requestTokenDto: RequestTokenDto,
    clientIp?: string,
    userAgent?: string,
  ): Promise<ValidateRO> {
    const { username, password } = requestTokenDto;

    try {
      const userToAttempt = await this.userModel
        .findOne({
          username,
        })
        .exec();

      if (!userToAttempt) {
        this.logger.warn(
          `Failed login attempt - User not found: ${username}, IP: ${clientIp}, UserAgent: ${userAgent}`,
        );
        throw new UnauthorizedException(MESSAGES.INVALID_CREDENTIALS);
      }

      if (userToAttempt.isArchived) {
        this.logger.warn(
          `Failed login attempt - Archived user: ${username}, IP: ${clientIp}, UserAgent: ${userAgent}`,
        );
        throw new UnauthorizedException(MESSAGES.INVALID_CREDENTIALS);
      }

      const isMatch = await userToAttempt.checkPassword(password);
      if (!isMatch) {
        this.logger.warn(
          `Failed login attempt - Invalid password: ${username}, IP: ${clientIp}, UserAgent: ${userAgent}`,
        );
        throw new UnauthorizedException(MESSAGES.INVALID_CREDENTIALS);
      }

      // Log successful login
      this.logger.log(
        `Successful login: ${username}, IP: ${clientIp}, UserAgent: ${userAgent}`,
      );

      // Continue with existing logic
      // Skip test result creation for demo accounts
      if (userToAttempt.role === UserRole.PATIENT) {
        if (userToAttempt.isFirstTimeLogin) {
          userToAttempt.sessionIBDSubtype = userToAttempt.ibdSubtype;
          await this.testService.createNewPatientTestResults(userToAttempt);
        } else {
          const needsSync =
            userToAttempt.ibdSubtype !== userToAttempt.sessionIBDSubtype;

          if (needsSync && userToAttempt.ibdSubtype) {
            userToAttempt.sessionIBDSubtype = userToAttempt.ibdSubtype;
            await this.testService.createTestForNewSubtype(userToAttempt);
          }
        }
      }

      if (userToAttempt.isFirstTimeLogin) {
        userToAttempt.isFirstTimeLogin = false;
      }
      const result = this.buildValidationRO(userToAttempt);
      userToAttempt.refreshToken = result.refreshToken;

      await userToAttempt.save();
      return result;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error(
        `Login error for ${username}: ${error.message}, IP: ${clientIp}`,
      );
      throw new UnauthorizedException(MESSAGES.INVALID_CREDENTIALS);
    }
  }

  async getValidatedUserById(id): Promise<ValidationUserRO> {
    const user = await this.userModel.findById(id);

    return this.buildUserRO(user);
  }

  async register(userDto: RegisterUserDto): Promise<ValidateRO> {
    const newUser = new this.userModel(userDto);
    await newUser.save();

    return this.buildValidationRO(newUser);
  }

  async requestNewAccessToken(
    requestAccessTokenDto: RequestAccessTokenDto,
  ): Promise<ValidateRO> {
    const { refreshToken, accessToken } = requestAccessTokenDto;
    const jwtBody = this.jwtService.decode(accessToken);
    const username = jwtBody.username;
    const user = await this.userModel.findOne({ username });
    if (user.isArchived) {
      throw new BadRequestException('User have been archived!');
    }
    try {
      await this.jwtService.verifyAsync(refreshToken, {
        secret: jwtRefreshSecret,
      });
    } catch (e) {
      throw new BadRequestException(e);
    }
    return this.buildValidationRO(user);
  }

  async logout(dto: LogoutDto, loggedUser) {
    await this.userModel.updateOne(
      { _id: loggedUser.id },
      {
        $set: { refreshToken: '', deviceTokens: [] },
      },
    );
  }

  async validateUser(
    payload: JwtPayload,
  ): Promise<RequestUserAttachmentInterface> {
    const user = await this.userModel
      .findOne({ username: payload.username })
      .select('fullName role username')
      .exec();

    if (!user) {
      throw new UnauthorizedException();
    }

    return {
      _id: user._id,
      username: user.username,
      name: user.fullName,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
    };
  }

  private buildValidationRO(user) {
    const refreshToken = this.createRefreshToken(user);
    const jwt = this.createJwtPayload(user, refreshToken);
    return {
      token: jwt,
      refreshToken,
      expiredIn: tokenExpired,
      user: this.buildUserRO(user),
    };
  }

  private buildUserRO(user): ValidationUserRO {
    return {
      username: user.username,
      role: user.role,
      isFirstTimeLogin: user.isFirstTimeLogin,
      isArchived: user.isArchived,
      firstName: user.firstName,
      lastName: user.lastName,
      ibdSubtype: user.ibdSubtype,
    };
  }

  private createRefreshToken(user) {
    const data: JwtPayload = {
      id: user.id,
      username: user.username,
      type: 'refresh',
    };
    return this.jwtService.sign(data, {
      secret: jwtRefreshSecret,
      expiresIn: refreshTokenExpired,
    });
  }

  private createJwtPayload(user, refreshToken: string) {
    const data: JwtPayload = {
      id: user.id,
      username: user.username,
    };
    return this.jwtService.sign(data, {
      secret: jwtSecret,
      expiresIn: tokenExpired,
    });
  }
}
