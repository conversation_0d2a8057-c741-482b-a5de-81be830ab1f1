import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { Controller, HttpStatus, Post, Get, Query, Res, UseGuards, UsePipes, ValidationPipe, Logger, InternalServerErrorException, BadRequestException, NotFoundException, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags, ApiResponse } from '@nestjs/swagger';
import { Permissions } from '../../shared/decorators/permission.decorator';
import { UserRole } from '../../shared/enums/user-role.enum';
import { TestType } from '../../shared/enums/test-type.enum';
import { ReportService } from './report.service';
import { filePathReport } from 'app.config';

@ApiTags('reports')
@ApiBearerAuth()
@Controller('reports')
export class ReportController {
  private readonly logger = new Logger(ReportController.name);

  constructor(private readonly reportService: ReportService) {}

  @ApiOperation({ description: ' Download Report ' })
  @Post('download')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @UsePipes(ValidationPipe)
  async downloadAnalyticsReport(@Res() res, @Query() query): Promise<any> {
    const path = await (await this.reportService.downloadReport(query)).replace('files/', '');

    return await res.sendFile(path, { root: filePathReport }, function (err) {
      if (err) {
        res
          .status(HttpStatus.NOT_FOUND)
          .json({
            statusCode: HttpStatus.NOT_FOUND,
            error: 'Not Found',
            message: `File ${path} is not found!`,
          })
          .end();
      }
    });
  }

  @ApiOperation({ description: 'Download CSV Report for Symptoms Test (Test 1)' })
  @Post('csv/symptoms')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @UsePipes(ValidationPipe)
  async downloadSymptomsCSV(@Res() res, @Query() query): Promise<any> {
    try {
      const path = await this.reportService.downloadCSVReport(TestType.SYMPTOMS, query);

      return await res.sendFile(path, { root: filePathReport }, (err) => {
        if (err) {
          this.logger.error(`Failed to send symptoms CSV file: ${err.message}`);
          if (!res.headersSent) {
            res
              .status(HttpStatus.NOT_FOUND)
              .json({
                statusCode: HttpStatus.NOT_FOUND,
                error: 'Not Found',
                message: `File ${path} is not found!`,
              })
              .end();
          }
        }
      });
    } catch (error) {
      this.logger.error(`Error generating symptoms CSV: ${error.message}`);
      if (error.message.includes('not found')) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to generate symptoms CSV report');
    }
  }

  @ApiOperation({ description: 'Download CSV Report for Mental Wellbeing Test (Test 2)' })
  @Post('csv/mental-wellbeing')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @UsePipes(ValidationPipe)
  async downloadMentalWellbeingCSV(@Res() res, @Query() query): Promise<any> {
    try {
      const path = await this.reportService.downloadCSVReport(TestType.MENTAL_WELLBEING, query);

      return await res.sendFile(path, { root: filePathReport }, (err) => {
        if (err) {
          this.logger.error(`Failed to send mental wellbeing CSV file: ${err.message}`);
          if (!res.headersSent) {
            res
              .status(HttpStatus.NOT_FOUND)
              .json({
                statusCode: HttpStatus.NOT_FOUND,
                error: 'Not Found',
                message: `File ${path} is not found!`,
              })
              .end();
          }
        }
      });
    } catch (error) {
      this.logger.error(`Error generating mental wellbeing CSV: ${error.message}`);
      if (error.message.includes('not found')) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to generate mental wellbeing CSV report');
    }
  }

  @ApiOperation({ description: 'Download CSV Report for General Wellbeing Test (Test 3)' })
  @Post('csv/general-wellbeing')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @UsePipes(ValidationPipe)
  async downloadGeneralWellbeingCSV(@Res() res, @Query() query): Promise<any> {
    try {
      const path = await this.reportService.downloadCSVReport(TestType.GENERAL_WELLBEING, query);

      return await res.sendFile(path, { root: filePathReport }, (err) => {
        if (err) {
          this.logger.error(`Failed to send general wellbeing CSV file: ${err.message}`);
          if (!res.headersSent) {
            res
              .status(HttpStatus.NOT_FOUND)
              .json({
                statusCode: HttpStatus.NOT_FOUND,
                error: 'Not Found',
                message: `File ${path} is not found!`,
              })
              .end();
          }
        }
      });
    } catch (error) {
      this.logger.error(`Error generating general wellbeing CSV: ${error.message}`);
      if (error.message.includes('not found')) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to generate general wellbeing CSV report');
    }
  }

  @ApiOperation({
    summary: 'Download CSV report for individual patient',
    description: 'Downloads a multi-sheet CSV file containing all data for an individual patient including patient info, symptoms, mental wellbeing, and general wellbeing test results as per functional requirement *******'
  })
  @ApiResponse({
    status: 200,
    description: 'Individual patient CSV file downloaded successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'There is currently no data available for this report.',
  })
  @ApiResponse({
    status: 404,
    description: 'Patient not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to generate individual patient CSV report',
  })
  @Get('patient/:patientId/csv')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async downloadIndividualPatientCSV(@Param('patientId') patientId: string, @Res() res): Promise<any> {
    try {
      const fileName = await this.reportService.downloadIndividualPatientCSV(patientId);

      return await res.sendFile(fileName, { root: filePathReport }, (err) => {
        if (err) {
          this.logger.error(`Failed to send individual patient CSV file: ${err.message}`);
          if (!res.headersSent) {
            res
              .status(HttpStatus.NOT_FOUND)
              .json({
                statusCode: HttpStatus.NOT_FOUND,
                error: 'Not Found',
                message: `File ${fileName} is not found!`,
              })
              .end();
          }
        }
      });
    } catch (error) {
      this.logger.error(`Error generating individual patient CSV: ${error.message}`);

      if (error instanceof NotFoundException) {
        throw error;
      }

      if (error.message.includes('no data available')) {
        throw new BadRequestException('There is currently no data available for this report.');
      }

      throw new InternalServerErrorException('Failed to generate individual patient CSV report');
    }
  }
}
