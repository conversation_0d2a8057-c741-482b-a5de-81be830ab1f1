import { Types } from 'mongoose';

export interface OverdueTestResultRO {
  id: Types.ObjectId;
  patientUsername: string;
  testName: string; // Formatted as "Test 1: Symptoms"
  dueDate: string; // DD/MM/YY format
  acknowledged: boolean;
  acknowledgedBy?: {
    id: Types.ObjectId;
    firstName: string;
    lastName: string;
    fullName: string;
  };
  acknowledgedAt?: string;
  notes: string;
  canEditNotes: boolean; // False once notes are saved
  patient: {
    id: Types.ObjectId;
    username: string;
    firstName: string;
    lastName: string;
  };
  testType: number;
  status: number;
}

export interface OverdueTestsWithPagination {
  items: OverdueTestResultRO[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  pagingCounter: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage?: number;
  nextPage?: number;
}
