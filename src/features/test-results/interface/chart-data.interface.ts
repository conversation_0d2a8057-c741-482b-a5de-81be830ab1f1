import { Types } from 'mongoose';

/**
 * Main response interface for patient chart data
 * Implements functional requirements 6.4.4.2
 */
export interface PatientChartDataRO {
  patient: PatientHeaderInfo;
  symptomsChart?: SymptomsChartData;
  mentalWellbeingChart?: MentalWellbeingChartData;
  generalWellbeingChart?: GeneralWellbeingChartData;
  tables: {
    symptoms?: TestResultTableData;
    mentalWellbeing?: TestResultTableData;
    generalWellbeing?: TestResultTableData;
  };
}

/**
 * Patient header information for chart view
 * As per requirements: "The page must show the patient's name and NHS number"
 */
export interface PatientHeaderInfo {
  id: string;
  fullName: string;
  nhsNumber: string;
}

/**
 * Symptoms chart data for HBI/SCCAI tests
 * Requirements: "RAG results will be displayed as a bar graph, y-axis = score, x-axis = date"
 * "The top of the graph will display which symptom survey was completed"
 */
export interface SymptomsChartData {
  title: string; // "HBI Survey" or "SCCAI Survey"
  data: SymptomsChartPoint[];
}

export interface SymptomsChartPoint {
  date: string; // ISO date string for x-axis
  score: number; // RAG score for y-axis
  ragFlags: {
    red: number;
    amber: number;
    green: number;
  };
}

/**
 * Mental Wellbeing chart data for A&D tests
 * Requirements: "A & D results will be displayed as a line graph"
 * "A & D will be displayed as separate lines, y-axis = score, x-axis = date"
 * "The points will be joined by a connecting line"
 */
export interface MentalWellbeingChartData {
  anxietyData: MentalWellbeingChartPoint[];
  depressionData: MentalWellbeingChartPoint[];
}

export interface MentalWellbeingChartPoint {
  date: string; // ISO date string for x-axis
  score: number; // Anxiety or Depression score for y-axis
}

/**
 * General Wellbeing chart data for SIBDQ tests
 * Requirements: "The scores will be displayed as a line graph"
 * "SIBDQ scores will be displayed as a line, y-axis = score, x-axis = date"
 * "The points will be joined by a connecting line"
 */
export interface GeneralWellbeingChartData {
  sibdqData: GeneralWellbeingChartPoint[];
}

export interface GeneralWellbeingChartPoint {
  date: string; // ISO date string for x-axis
  score: number; // SIBDQ score for y-axis
}

/**
 * Table data with pagination for chart view
 * Requirements: "Each data table within the patient results page will be scrollable"
 * "Each table will be defaulted to 10 items per page, Apply pagination"
 */
export interface TestResultTableData {
  items: TestResultTableRow[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
}

/**
 * Table row data for different test types
 * Supports all three test types with optional fields
 */
export interface TestResultTableRow {
  id: string;
  dateTime: string; // UTC timestamp as per user preferences
  score?: number; // Total score for the test
  
  // Symptoms test specific fields
  ragFlags?: {
    red: number;
    amber: number;
    green: number;
  };
  
  // Mental Wellbeing test specific fields
  anxietyScore?: number;
  depressionScore?: number;
  
  // General Wellbeing test specific fields
  sibdqScore?: number;
  eqScore?: number;
  
  // Common fields for all test types
  incomplete: boolean; // "I" field - indicates incomplete test
  flagged: boolean; // "Flag?" field - indicates if test is flagged
  notes?: string; // Clinician notes
}

/**
 * Single test type chart data response
 * Used for endpoints that return data for specific test type
 */
export interface SingleTestChartDataRO {
  patient: PatientHeaderInfo;
  chartData: SymptomsChartData | MentalWellbeingChartData | GeneralWellbeingChartData;
  tableData: TestResultTableData;
}

/**
 * Chart data query parameters
 * For pagination and filtering of chart data
 */
export interface ChartDataQuery {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * Pagination metadata for chart responses
 */
export interface ChartPaginationMetadata {
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
}

/**
 * Error response for chart data endpoints
 * Implements required error messages from functional requirements
 */
export interface ChartDataErrorRO {
  statusCode: number;
  message: string;
  error: string;
}

/**
 * Raw test result data for chart processing
 * Internal interface for data transformation
 */
export interface RawTestResultForChart {
  _id: Types.ObjectId;
  completedDate: Date;
  totalScores: number;
  reds: number;
  ambers: number;
  greens: number;
  anxietyScore?: number;
  depressionScore?: number;
  sibdqScore?: number;
  eqScore?: number;
  status: number;
  flagDetails?: any[];
  patient: {
    _id: Types.ObjectId;
    firstName: string;
    lastName: string;
    fullName: string;
    nhsNumber: string;
    ibdSubtype?: string;
  };
}

/**
 * Chart data aggregation result
 * Internal interface for database aggregation results
 */
export interface ChartDataAggregationResult {
  symptoms: RawTestResultForChart[];
  mentalWellbeing: RawTestResultForChart[];
  generalWellbeing: RawTestResultForChart[];
}
