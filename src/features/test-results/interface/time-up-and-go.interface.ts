import { TestResultStatus } from '@aos-enum/test-result-status.enum';
import { TestType } from '@aos-enum/test-type.enum';

export interface ResultInterface {
  timeUpAndGo: TestResultInterface[];
  symptoms: TestResultInterface[];
  healthStatus: TestResultInterface[];
}

export interface TestResultInterface {
  username: string;
  fullName: string;
  nhsNumber: string;
  records: TestResultDetailInterface[];
}

export interface TestResultDetailInterface {
  id: string;
  greens: number;
  ambers: number;
  reds: number;
  TestType: TestType;
  redFlag: boolean;
  status: TestResultStatus;
  notes: string;
  acknowledgedBy: any;
  clinicalSummaryScore: number;
  totalScores: number;
  startDate: Date;
  physicalLimitationScore: number;
  symptomStabilityScore: number;
  symptomFrequencyScore: number;
  symptomBurdenScore: number;
  totalSymptomScore: number;
  selfEfficacyScore: number;
  qualityOfLifeScore: number;
  socialLimitationScore: number;
  ttc: number;
  report: Array<{
    redFlag: boolean;
    code: string;
    question: string;
    answer: string;
    rag: string;
    score: number;
  }>;
}
