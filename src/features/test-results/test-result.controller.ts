import { Permissions } from '@aos-decorator/permission.decorator';
import { User } from '@aos-decorator/user.decorator';
import { UserRole } from '@aos-enum/user-role.enum';
import { PermissionGuard } from '@aos-guard/permission.guard';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Query,
  UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AcknowledgeOverdueTestWithNotesDto, AcknowledgeRedFlagDto, GetOverdueTestsDto, GetPatientDetailsDto, ReportQueryDto } from './dto';
import { ChartQueryDto, SingleTestChartQueryDto } from './dto/chart-query.dto';
import { OverdueTestsWithPagination, TestResultsWithPagination, PatientDetailsWithPagination } from './interface';
import { PatientChartDataRO, SingleTestChartDataRO } from './interface/chart-data.interface';
import { TestResultService } from './test-result.service';

@ApiBearerAuth()
@ApiTags('test-results')
@Controller('test-results')
export class TestResultController {
  private readonly logger = new Logger(TestResultController.name);

  constructor(private readonly testResultService: TestResultService) {}

  @ApiOperation({ description: ' Get test results have flag' })
  @Get('has-flag')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async getFlagTestResults(@Query() query): Promise<TestResultsWithPagination> {
    return await this.testResultService.getFlagTestResults(query);
  }

  @ApiOperation({
    description: 'Get test results by type with enhanced sorting options (1: Symptoms, 2: Mental Wellbeing, 3: General Wellbeing). Supports sorting by subtype, completed status, and overdue status.',
  })
  @Get('report')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async getReportByType(@Query() query: ReportQueryDto): Promise<TestResultsWithPagination> {
    return await this.testResultService.getReportByType(query);
  }

  @ApiOperation({ description: ' Get test results of specific patient ' })
  @Get('patient/:id/type/:type')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async getPatientTestResults(@Param('id') id: string, @Param('type') type: number, @Query() query) {
    return await this.testResultService.getPatientTestResults(id, type, query);
  }

  @ApiOperation({
    summary: 'Get patient details with historical test results',
    description: 'Opens patient details and displays all historical test results with flagged results highlighting and hover-over RAG score breakdown functionality as per functional requirements *******'
  })
  @ApiResponse({
    status: 200,
    description: 'Patient details with historical test results retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Patient not found',
  })
  @Get('patient/:id/details')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async getPatientDetails(
    @Param('id') patientId: string,
    @Query() query: GetPatientDetailsDto
  ): Promise<PatientDetailsWithPagination> {
    try {
      return await this.testResultService.getPatientDetails(patientId, query);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to retrieve patient details', error);
      throw new InternalServerErrorException(
        'Unable to retrieve patient details. Please refresh the page or try again later.'
      );
    }
  }

  @ApiOperation({ description: ' Submit test ' })
  @Patch(':id')
  @Permissions(UserRole.PATIENT, UserRole.DEMO)
  @UseGuards(AuthGuard, PermissionGuard)
  async submitTest(@Body() updateData, @User() user, @Param('id') id: string) {
    return await this.testResultService.submitTest(id, user, updateData);
  }

  @ApiOperation({ 
    summary: 'Get overdue tests for overdue tests table',
    description: 'Retrieves paginated list of overdue tests with sorting support for Patient Username and Test Name columns'
  })
  @ApiResponse({
    status: 200,
    description: 'Overdue tests retrieved successfully',
  })
  @ApiResponse({
    status: 500,
    description: 'Unable to retrieve overdue tests. Please refresh the page or try again later.',
  })
  @Get('overdue')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async getOverdueTests(@Query() query: GetOverdueTestsDto): Promise<OverdueTestsWithPagination> {
    try {
      return await this.testResultService.getOverdueTests(query);
    } catch (error) {
      this.logger.error('Failed to retrieve overdue tests', error);
      throw new InternalServerErrorException(
        'Unable to retrieve overdue tests. Please refresh the page or try again later.'
      );
    }
  }

  @ApiOperation({
    summary: 'Acknowledge overdue test with notes',
    description: 'Adds notes and acknowledges overdue test in one atomic operation. Notes are required for acknowledgment as per functional requirements *******.'
  })
  @ApiResponse({
    status: 204,
    description: 'Overdue test acknowledged with notes successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Unable to acknowledge this flag. Please try again.',
  })
  @ApiResponse({
    status: 404,
    description: 'Test result not found',
  })
  @Patch(':id/acknowledge-overdue')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async acknowledgeOverdueTestCheckbox(
    @Body() updateData: AcknowledgeOverdueTestWithNotesDto,
    @User() user,
    @Param('id') id: string
  ): Promise<void> {
    try {
      return await this.testResultService.acknowledgeOverdueTest(id, user, updateData);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error('Failed to acknowledge overdue test with notes', error);
      throw new BadRequestException('Unable to acknowledge this flag. Please try again.');
    }
  }

  @ApiOperation({
    summary: 'Acknowledge red flag with notes',
    description: 'Adds notes and acknowledges red flag in one atomic operation. Notes are required for acknowledgment and cannot be edited once saved as per functional requirements *******.'
  })
  @ApiResponse({
    status: 204,
    description: 'Red flag acknowledged with notes successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Unable to acknowledge this red flag. Please try again.',
  })
  @ApiResponse({
    status: 404,
    description: 'Test result not found',
  })
  @Patch(':id/acknowledge-red-flag')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  async acknowledgeRedFlag(
    @Body() updateData: AcknowledgeRedFlagDto,
    @User() user,
    @Param('id') id: string
  ): Promise<void> {
    try {
      return await this.testResultService.acknowledgeRedFlag(id, user, updateData);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error('Failed to acknowledge red flag with notes', error);
      throw new BadRequestException('Unable to acknowledge this red flag. Please try again.');
    }
  }

  /**
   * Get chart data for all test types for a specific patient
   * Implements functional requirement *******: "The Clinician will be able to view graphic results for an individual patient"
   */
  @Get('patient/:patientId/charts')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiOperation({
    summary: 'Get chart data for all test types for a patient',
    description: 'Retrieves formatted chart and table data for symptoms, mental wellbeing, and general wellbeing tests for graphic visualization',
  })
  @ApiResponse({
    status: 200,
    description: 'Chart data retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Patient not found',
  })
  @ApiResponse({
    status: 500,
    description: 'We couldn\'t load the graph for this test. Please refresh or try again later.',
  })
  async getPatientChartData(
    @Param('patientId') patientId: string,
    @Query() query: ChartQueryDto,
  ): Promise<PatientChartDataRO> {
    try {
      return await this.testResultService.getPatientChartData(patientId, query);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error('Failed to retrieve patient chart data', error);
      throw new InternalServerErrorException('We couldn\'t load the graph for this test. Please refresh or try again later.');
    }
  }

  /**
   * Get chart data for a specific test type for a patient
   * Implements functional requirement ******* for individual test type visualization
   */
  @Get('patient/:patientId/charts/:testType')
  @Permissions(UserRole.CLINICIAN, UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiOperation({
    summary: 'Get chart data for specific test type for a patient',
    description: 'Retrieves formatted chart and table data for a specific test type (symptoms, mental wellbeing, or general wellbeing)',
  })
  @ApiResponse({
    status: 200,
    description: 'Chart data retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Patient not found or no data available for this test type',
  })
  @ApiResponse({
    status: 500,
    description: 'We couldn\'t load the graph for this test. Please refresh or try again later.',
  })
  async getPatientChartDataByType(
    @Param('patientId') patientId: string,
    @Param('testType') testType: number,
    @Query() query: SingleTestChartQueryDto,
  ): Promise<SingleTestChartDataRO> {
    try {
      return await this.testResultService.getPatientChartDataByType(patientId, testType, query);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error('Failed to retrieve patient chart data by type', error);
      throw new InternalServerErrorException('We couldn\'t load the graph for this test. Please refresh or try again later.');
    }
  }
}
