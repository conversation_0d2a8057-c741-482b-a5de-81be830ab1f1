import { Injectable, Logger, Inject } from '@nestjs/common';
import { FlagType, SymptomsTestSubtype } from '@aos-enum/flag-threshold.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { RAGType } from '@aos-enum/rag-type.enum';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';

// Interface to avoid circular dependency
interface IRedFlagEmailService {
  sendRedFlagAlert(patient: any, testResult: any, flagDetails: any): Promise<void>;
}

export interface FlagLogData {
  flagType: FlagType;
  flagReason: string;
  patientUsername: string;
  testType: TestType;
  testSubtype?: SymptomsTestSubtype;
  scoreDifference?: number;
  currentScore?: number;
  previousScore?: number;
  anxietyScore?: number;
  depressionScore?: number;
  daysSinceLastTest?: number;
  questionNumbers?: string[];
}

@Injectable()
export class FlagLoggingService {
  private readonly logger = new Logger(FlagLoggingService.name);

  constructor(
    @Inject('RedFlagEmailService')
    private readonly redFlagEmailService: IRedFlagEmailService
  ) {}

  /**
   * Log a flag with detailed information for audit trail
   */
  async logFlag(testResult: any, flagData: FlagLogData): Promise<void> {
    try {
      if (!testResult.flagDetails) {
        testResult.flagDetails = [];
      }

      const flagDetail = {
        flagType: flagData.flagType,
        flagReason: flagData.flagReason,
        flaggedAt: new Date(),
        patientUsername: flagData.patientUsername,
        testType: flagData.testType,
        testSubtype: flagData.testSubtype,
        scoreDifference: flagData.scoreDifference,
        currentScore: flagData.currentScore,
        previousScore: flagData.previousScore,
        anxietyScore: flagData.anxietyScore,
        depressionScore: flagData.depressionScore,
        daysSinceLastTest: flagData.daysSinceLastTest,
        questionNumbers: flagData.questionNumbers || [],
      };

      testResult.flagDetails.push(flagDetail);

      // Set redFlag to true for the test result
      testResult.redFlag = true;

      this.logger.log(`Flag logged: ${flagData.flagType} for patient ${flagData.patientUsername} - ${flagData.flagReason}`);

      // Send red flag email notification
      await this.sendRedFlagEmailNotification(testResult, flagDetail);
    } catch (error) {
      this.logger.error(`Failed to log flag: ${error.message}`, error.stack);
    }
  }

  /**
   * Log Red/Amber result flag
   */
  async logRedAmberFlag(
    testResult: any, 
    patientUsername: string, 
    ragType: RAGType, 
    questionNumbers: string[],
    testSubtype?: SymptomsTestSubtype
  ): Promise<void> {
    const flagReason = `${ragType === RAGType.R ? 'Red' : 'Amber'} result detected in questions: ${questionNumbers.join(', ')}`;
    
    await this.logFlag(testResult, {
      flagType: FlagType.RED_AMBER_RESULT,
      flagReason,
      patientUsername,
      testType: testResult.type,
      testSubtype,
      questionNumbers,
    });
  }

  /**
   * Log score increase flag (single test increase)
   */
  async logScoreIncreaseFlag(
    testResult: any,
    patientUsername: string,
    scoreDiff: number,
    currentScore: number,
    prevScore: number,
    testSubtype: SymptomsTestSubtype,
    threshold: number
  ): Promise<void> {
    const flagReason = `Score increased by ${scoreDiff} (from ${prevScore} to ${currentScore}), exceeding threshold of ${threshold}`;
    
    await this.logFlag(testResult, {
      flagType: FlagType.SCORE_INCREASE,
      flagReason,
      patientUsername,
      testType: testResult.type,
      testSubtype,
      scoreDifference: scoreDiff,
      currentScore,
      previousScore: prevScore,
    });
  }

  /**
   * Log sustained increase flag (2 consecutive weeks)
   */
  async logSustainedIncreaseFlag(
    testResult: any,
    patientUsername: string,
    scoreDiff: number,
    currentScore: number,
    prevScore: number,
    testSubtype: SymptomsTestSubtype,
    threshold: number
  ): Promise<void> {
    const flagReason = `Sustained score increase of ${scoreDiff} for 2 consecutive weeks (from ${prevScore} to ${currentScore}), exceeding threshold of ${threshold}`;
    
    await this.logFlag(testResult, {
      flagType: FlagType.SUSTAINED_INCREASE,
      flagReason,
      patientUsername,
      testType: testResult.type,
      testSubtype,
      scoreDifference: scoreDiff,
      currentScore,
      previousScore: prevScore,
    });
  }

  /**
   * Log mental wellbeing threshold flag
   */
  async logMentalWellbeingFlag(
    testResult: any,
    patientUsername: string,
    anxietyScore: number,
    depressionScore: number,
    threshold: number
  ): Promise<void> {
    const exceededScores = [];
    if (anxietyScore > threshold) {
      exceededScores.push(`Anxiety: ${anxietyScore}`);
    }
    if (depressionScore > threshold) {
      exceededScores.push(`Depression: ${depressionScore}`);
    }
    
    const flagReason = `Mental wellbeing scores exceeded threshold of ${threshold}: ${exceededScores.join(', ')}`;
    
    await this.logFlag(testResult, {
      flagType: FlagType.MENTAL_THRESHOLD,
      flagReason,
      patientUsername,
      testType: testResult.type,
      anxietyScore,
      depressionScore,
    });
  }

  /**
   * Log overdue test flag
   */
  async logOverdueFlag(
    testResult: any,
    patientUsername: string,
    daysSinceLastTest: number,
    testTypeName: string
  ): Promise<void> {
    // Enhanced flag reason with IBD subtype for symptoms tests
    let flagReason = `Test overdue by ${daysSinceLastTest} days since last completed ${testTypeName} test`;

    // Add IBD subtype information for symptoms tests as per requirements 6.4.3.4
    if (testResult.type === TestType.SYMPTOMS && testResult.patient?.ibdSubtype) {
      const ibdSubtype = testResult.patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE ? SymptomsTestSubtype.HBI :
                        testResult.patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS ? SymptomsTestSubtype.SCCAI : '';
      if (ibdSubtype) {
        flagReason = `Test overdue by ${daysSinceLastTest} days since last completed ${ibdSubtype} ${testTypeName} test`;
      }
    }

    await this.logFlag(testResult, {
      flagType: FlagType.OVERDUE,
      flagReason,
      patientUsername,
      testType: testResult.type,
      daysSinceLastTest,
      testSubtype: testResult.type === TestType.SYMPTOMS && testResult.patient?.ibdSubtype ?
        (testResult.patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE ? SymptomsTestSubtype.HBI :
         testResult.patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS ? SymptomsTestSubtype.SCCAI : null) : null,
    });
  }

  /**
   * Get flag summary for a test result
   */
  getFlagSummary(testResult: any): string[] {
    if (!testResult.flagDetails || testResult.flagDetails.length === 0) {
      return [];
    }

    return testResult.flagDetails.map((flag: any) => flag.flagReason);
  }

  /**
   * Check if test result has specific flag type
   */
  hasFlagType(testResult: any, flagType: FlagType): boolean {
    if (!testResult.flagDetails) {
      return false;
    }

    return testResult.flagDetails.some((flag: any) => flag.flagType === flagType);
  }

  /**
   * Send red flag email notification
   */
  private async sendRedFlagEmailNotification(testResult: any, flagDetail: any): Promise<void> {
    try {
      // Only send email for red flags that should trigger notifications
      const shouldSendEmail = this.shouldSendEmailForFlag(flagDetail.flagType);

      if (!shouldSendEmail) {
        this.logger.debug(`[PatientID: ${testResult.patient?._id || 'unknown'}] [FlagType: ${flagDetail.flagType}] [Action: email_skipped] - Flag type does not require email notification`);
        return;
      }

      // Ensure patient data is available
      if (!testResult.patient) {
        this.logger.warn(`[TestResultID: ${testResult._id || 'unknown'}] [FlagType: ${flagDetail.flagType}] [Action: email_failed] - Patient data not available`);
        return;
      }

      // Validate required patient fields
      if (!testResult.patient._id && !testResult.patient.id) {
        this.logger.warn(`[PatientID: unknown] [FlagType: ${flagDetail.flagType}] [Action: email_failed] - Patient ID not available`);
        return;
      }

      const patientId = testResult.patient._id || testResult.patient.id;

      this.logger.log(`[PatientID: ${patientId}] [FlagType: ${flagDetail.flagType}] [Action: email_sending] - Sending red flag email notification`);

      await this.redFlagEmailService.sendRedFlagAlert(
        testResult.patient,
        testResult,
        flagDetail
      );

      this.logger.log(`[PatientID: ${patientId}] [FlagType: ${flagDetail.flagType}] [Action: email_sent] - Red flag email notification sent successfully`);
    } catch (error) {
      const patientId = testResult.patient?._id || testResult.patient?.id || 'unknown';
      this.logger.error(`[PatientID: ${patientId}] [FlagType: ${flagDetail.flagType}] [Action: email_error] - Failed to send red flag email notification: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the flag logging process
    }
  }

  /**
   * Determine if email should be sent for this flag type
   * Based on functional requirements section 6.4.3.5
   */
  private shouldSendEmailForFlag(flagType: FlagType): boolean {
    // Send emails for all flag types as per requirements:
    // - Symptoms Test flags (RED_AMBER_RESULT, SCORE_INCREASE, SUSTAINED_INCREASE)
    // - Mental Wellbeing Test scores > 11 (MENTAL_THRESHOLD)
    // - Overdue test flags (OVERDUE)
    return [
      FlagType.RED_AMBER_RESULT,
      FlagType.SCORE_INCREASE,
      FlagType.SUSTAINED_INCREASE,
      FlagType.MENTAL_THRESHOLD,
      FlagType.OVERDUE
    ].includes(flagType);
  }
}
