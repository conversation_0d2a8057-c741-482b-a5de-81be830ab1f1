import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsN<PERSON>ber, Min, Max, IsDateString, IsIn } from 'class-validator';

/**
 * DTO for chart data query parameters
 * Implements pagination and filtering for chart data endpoints
 * As per functional requirements *******: "Each table will be defaulted to 10 items per page"
 */
export class ChartQueryDto {
  @ApiPropertyOptional({
    description: 'Page number for table pagination',
    minimum: 1,
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page for table data',
    minimum: 1,
    maximum: 100,
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Start date for filtering chart data (ISO date string)',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering chart data (ISO date string)',
    example: '2025-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Field to sort table data by',
    enum: ['completedDate', 'score', 'anxietyScore', 'depressionScore', 'sibdqScore'],
    example: 'completedDate',
    default: 'completedDate',
  })
  @IsOptional()
  @IsIn(['completedDate', 'score', 'anxietyScore', 'depressionScore', 'sibdqScore'])
  sortBy?: string = 'completedDate';

  @ApiPropertyOptional({
    description: 'Sort order for table data',
    enum: ['asc', 'desc'],
    example: 'desc',
    default: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: string = 'desc';
}

/**
 * DTO for single test type chart query
 * Used for endpoints that return data for specific test type
 */
export class SingleTestChartQueryDto extends ChartQueryDto {
  @ApiPropertyOptional({
    description: 'Include chart data in response',
    example: true,
    default: true,
  })
  @IsOptional()
  @Type(() => Boolean)
  includeChart?: boolean = true;

  @ApiPropertyOptional({
    description: 'Include table data in response',
    example: true,
    default: true,
  })
  @IsOptional()
  @Type(() => Boolean)
  includeTable?: boolean = true;
}
