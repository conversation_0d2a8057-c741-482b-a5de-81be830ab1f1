import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsN<PERSON><PERSON>, Min, Max, IsIn } from 'class-validator';

/**
 * DTO for getReportByType query parameters
 * Supports sorting by various criteria including new subtype, completed status, and overdue status
 */
export class ReportQueryDto {
  @ApiPropertyOptional({
    description: 'Test type to filter by',
    enum: [1, 2, 3],
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsIn([1, 2, 3])
  type?: number;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: ['completedDate', 'username', 'status', 'subtype', 'completed', 'overdue'],
    example: 'completedDate',
    default: 'completedDate',
  })
  @IsOptional()
  // @IsIn(['completedDate', 'username', 'status', 'subtype', 'completed', 'overdue', 'reds', 'greens', 'ambers'])
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    example: 'desc',
    default: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}
