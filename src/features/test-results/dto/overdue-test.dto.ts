import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

export class GetOverdueTestsDto {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: ['username', 'testName', 'dueDate'],
    example: 'username',
  })
  @IsOptional()
  @IsIn(['username', 'testName', 'dueDate'])
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    example: 'asc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

export class AcknowledgeOverdueTestDto {
  @ApiProperty({
    description: 'Notes for the overdue test acknowledgment (immutable once saved)',
    example: 'Patient contacted and scheduled for follow-up appointment.',
  })
  @IsNotEmpty()
  @IsString()
  notes: string;
}

export class AcknowledgeOverdueTestWithNotesDto {
  @ApiProperty({
    description: 'Notes for the overdue test acknowledgment (required and immutable once saved)',
    example: 'Patient contacted and scheduled for follow-up appointment.',
  })
  @IsNotEmpty()
  @IsString()
  notes: string;
}

export class AcknowledgeRedFlagDto {
  @ApiProperty({
    description: 'Notes for the red flag acknowledgment (required and immutable once saved)',
    example: 'Reviewed patient symptoms, scheduled follow-up appointment for next week.',
  })
  @IsNotEmpty()
  @IsString()
  notes: string;
}

export class GetPatientDetailsDto {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: ['completedDate', 'startDate', 'type'],
    example: 'completedDate',
  })
  @IsOptional()
  @IsIn(['completedDate', 'startDate', 'type'])
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}
