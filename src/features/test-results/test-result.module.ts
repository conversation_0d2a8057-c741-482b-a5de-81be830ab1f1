import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LoggerModule } from '../../logger/logger.module';

import { CalculatorService } from './calculator.service';
import { ReportController } from './report.controller';
import { ReportService } from './report.service';
import { TestResultController } from './test-result.controller';
import { TestResultService } from './test-result.service';

import { DatabaseModule } from '@aos-database/database.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { FirebaseService } from '../../shared/services/firebase.service';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
    LoggerModule.forRoot(),
  ],
  providers: [TestResultService, CalculatorService, ReportService, FirebaseService],
  controllers: [TestResultController, ReportController],
})
export class TestResultModule {}
