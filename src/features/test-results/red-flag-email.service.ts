import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { clinicianPortalUrl, smtpEmail, smtpPass, smtpPort, smtpSenderName, smtpUrl, smtpUser } from '../../app.config';
import { AppConfigService } from '../../shared/services/app-config.service';
import { DeepLinkService } from '../../shared/services/deep-link.service';
import { HtmlSanitizerService } from '../../shared/services/html-sanitizer.service';
import { TestResultService } from './test-result.service';
import * as nodemailer from 'nodemailer';
import { TestTitle } from '@aos-enum/test-title.enum';
import { TestResultStatus } from '@aos-enum/test-result-status.enum';
import { SymptomsTestSubtype } from '@aos-enum/flag-threshold.enum';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { TestType } from '@aos-enum/test-type.enum';

@Injectable()
export class RedFlagEmailService {
  private readonly logger = new Logger(RedFlagEmailService.name);
  private readonly smtpTransporter: nodemailer.Transporter;

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly deepLinkService: DeepLinkService,
    private readonly htmlSanitizerService: HtmlSanitizerService,
  ) {
    // Initialize SMTP transporter with the same configuration as the helper
    this.smtpTransporter = nodemailer.createTransport({
      host: smtpUrl,
      port: smtpPort,
      secure: true,
      pool: true,
      maxConnections: 3,
      maxMessages: 50,
      connectionTimeout: 10000,
      greetingTimeout: 5000,
      socketTimeout: 10000,
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    });
  }

  async sendRedFlagAlert(patient: any, testResult: any, flagDetails: any): Promise<void> {
    const patientId = patient._id || patient.id || 'unknown';
    const testType = testResult.type || 'unknown';
    const flagType = flagDetails.flagType || 'unknown';

    this.logger.log(
      `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_start] - Starting red flag email notification`,
    );

    try {
      await this.sendEmailWithRetry(patient, testResult, patientId, testType, flagType);

      this.logger.log(
        `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_success] - Red flag email sent successfully`,
      );
    } catch (error) {
      this.logger.error(
        `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_failed] - Failed to send red flag email after all retries: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async generateEmailContent(patient: any, testResult: any) {
    const templatePath = join(process.cwd(), 'src/assets/email-templates/red-flag-alert.html');
    let htmlTemplate = readFileSync(templatePath, 'utf8');

    // Get red flag email for template
    const redFlagEmail = await this.appConfigService.getRedFlagEmail();

    // Generate patient name with XSS prevention
    const patientName = this.sanitizeForEmail(`${patient.firstName || ''} ${patient.lastName || ''}`.trim());

    // Generate test name with XSS prevention using the same format as the Red Flag & Overdue table
    const testName = this.sanitizeForEmail(this.formatTestNameForEmail(testResult));

    // Generate question numbers if applicable with XSS prevention
    let questionNumber = '';
    if (testResult.report && Array.isArray(testResult.report)) {
      const flaggedQuestions = testResult.report
        .filter((item: any) => item.redFlag)
        .map((item: any) => this.sanitizeForEmail(item.code || ''))
        .join(', ');
      questionNumber = flaggedQuestions || 'N/A';
    } else {
      questionNumber = 'N/A';
    }

    // Generate deep link to patient profile
    const deepLink = this.generateDeepLink(patient?._id || patient?.id, patient?.username);

    // Portal link (same as clinician portal for now)
    const portalLink = clinicianPortalUrl;

    // Replace placeholders in template with sanitized data
    htmlTemplate = htmlTemplate
      .replace(/#RedFlagEmail#/g, this.sanitizeForEmail(redFlagEmail))
      .replace(/#PatientName#/g, patientName)
      .replace(/#NHSNumber#/g, this.sanitizeForEmail(patient.nhsNumber || 'N/A'))
      .replace(/#TestName#/g, testName)
      .replace(/#QuestionNumber#/g, questionNumber)
      .replace(/#DeepLink#/g, this.sanitizeForEmail(deepLink))
      .replace(/#PortalLink#/g, this.sanitizeForEmail(portalLink));

    // Generate plain text version using secure HTML sanitizer
    const textContent = this.htmlSanitizerService.htmlToText(htmlTemplate);

    return {
      html: htmlTemplate,
      text: textContent,
    };
  }

  private generateDeepLink(patientId: string, patientUsername: string): string {
    return this.deepLinkService.generatePatientProfileLink(patientId, patientUsername);
  }



  /**
   * Send email with retry mechanism (3 attempts with exponential backoff)
   */
  private async sendEmailWithRetry(
    patient: any,
    testResult: any,
    patientId: string,
    testType: string,
    flagType: string,
    maxRetries: number = 3,
  ): Promise<void> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.debug(
          `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_attempt] - Attempt ${attempt}/${maxRetries}`,
        );

        const redFlagEmail = await this.appConfigService.getRedFlagEmail();
        const emailContent = await this.generateEmailContent(patient, testResult);

        // Use SMTP helper function to send email with proper error handling
        await this.sendEmailViaSMTP(redFlagEmail, 'Red Flag Alert', emailContent.html);

        // Success - no need to retry
        return;
      } catch (error) {
        lastError = error;
        this.logger.warn(
          `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_retry] - Attempt ${attempt}/${maxRetries} failed: ${error.message}`,
        );

        // Don't wait after the last attempt
        if (attempt < maxRetries) {
          const delayMs = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
          this.logger.debug(
            `[PatientID: ${patientId}] [TestType: ${testType}] [FlagType: ${flagType}] [Action: email_delay] - Waiting ${delayMs}ms before retry`,
          );
          await this.delay(delayMs);
        }
      }
    }

    // All retries failed
    throw lastError;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Format test name for email using the same format as the Red Flag & Overdue table
   * This ensures consistency between the table display and email notifications
   */
  private formatTestNameForEmail(testResult: any): string {
    // Start with base test name
    let title = this.getBaseTestName(testResult.type);

    // Handle red flag tests with specific formatting
    if (testResult.redFlag) {
      if (testResult.type === TestType.SYMPTOMS && testResult.patient?.ibdSubtype) {
        const ibdPrefix =
          testResult.patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE
            ? SymptomsTestSubtype.HBI
            : testResult.patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
              ? SymptomsTestSubtype.SCCAI
              : '';
        title = `Test ${TestType.SYMPTOMS} - ${ibdPrefix} Symptoms`;
      }

      if (testResult.type === TestType.MENTAL_WELLBEING) {
        const anxietyScore = testResult.anxietyScore || 0;
        const depressionScore = testResult.depressionScore || 0;

        if (anxietyScore > 11 && depressionScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_ANXIETY_DEPRESSION;
        } else if (anxietyScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_ANXIETY;
        } else if (depressionScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_DEPRESSION;
        }
      }

      // Add flagged question numbers if available
      if (testResult.report) {
        const flaggedQuestions = testResult.report
          .filter((item: any) => item.redFlag)
          .map((item: any) => `Q${item.order}`)
          .join(', ');

        if (flaggedQuestions) {
          title = `${title} - ${flaggedQuestions}`;
        }
      }
    }

    // Handle overdue tests with days calculation
    if (testResult.status === TestResultStatus.OVERDUE) {
      return this.formatOverdueTestName(testResult);
    }

    return title;
  }

  /**
   * Get base test name by type
   */
  private getBaseTestName(testType: number): string {
    const testNames = {
      1: 'Test 1 - Symptoms', // TestType.SYMPTOMS
      2: 'Test 2 - Mental Wellbeing', // TestType.MENTAL_WELLBEING
      3: 'Test 3 - General Wellbeing', // TestType.GENERAL_WELLBEING
    };

    return testNames[testType] || `Test ${testType}`;
  }

  /**
   * Format overdue test name with exact days overdue (matches the Red Flag & Overdue table format)
   */
  private formatOverdueTestName(testResult: any): string {
    try {
      const daysOverdue = TestResultService.calculateExactDaysOverdue(testResult);

      if (testResult.type === TestType.SYMPTOMS) {
        // Get IBD subtype from patient data
        const ibdSubtype = testResult.patient?.ibdSubtype;
        if (ibdSubtype === IBDSubtype.CROHNS_DISEASE) {
          return `Test 1 - HBI Symptoms - ${daysOverdue} days`;
        } else if (ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS) {
          return `Test 1 - SCCAI Symptoms - ${daysOverdue} days`;
        } else {
          return `Test 1 - Symptoms - ${daysOverdue} days`;
        }
      } else if (testResult.type === TestType.MENTAL_WELLBEING) {
        return `Test 2 - Mental Wellbeing - ${daysOverdue} days`;
      } else if (testResult.type === TestType.GENERAL_WELLBEING) {
        return `Test 3 - General Wellbeing - ${daysOverdue} days`;
      }

      return `Test ${testResult.type} - ${daysOverdue} days`;
    } catch (error) {
      // Error handling as specified in requirements
      return TestResultService.formatTestNameWithError(testResult.type);
    }
  }



  /**
   * Sanitize data for email content to prevent XSS
   * Uses the secure HTML sanitizer service
   */
  private sanitizeForEmail(input: string): string {
    return this.htmlSanitizerService.sanitizeForHtml(input);
  }

  /**
   * Send email via SMTP with proper error handling for retry logic
   */
  private async sendEmailViaSMTP(toEmail: string, subject: string, htmlContent: string): Promise<void> {
    const mailInfo = {
      from: `${smtpSenderName} ${smtpEmail}`, // sender address (same format as original helper)
      to: toEmail, // list of receivers
      subject, // Subject line
      text: htmlContent, // plain text body
      html: htmlContent, // html body
    };

    try {
      const result = await this.smtpTransporter.sendMail(mailInfo);
      this.logger.debug(`SMTP email sent successfully to: ${toEmail}`, result);
    } catch (error) {
      // Re-throw the error to maintain compatibility with retry logic
      this.logger.error(`SMTP email sending failed to: ${toEmail}`, error);
      throw new Error(`SMTP email sending failed: ${error.message}`);
    }
  }
}
