import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { QuestionType } from '@aos-enum/question-type.enum';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { NumericQuestionCode } from '../../shared/enums/numeric-question.enum';
import { timedUpPredefinedThreshold } from './../../app.config';
import { FlagThreshold, SymptomsTestSubtype } from './../../shared/enums/flag-threshold.enum';
import { RAGType } from './../../shared/enums/rag-type.enum';
import { TestResultStatus } from './../../shared/enums/test-result-status.enum';
import { TestType } from './../../shared/enums/test-type.enum';

import { TestService } from './../tests/test.service';
import { FlagLoggingService } from './flag-logging.service';
import {
  DatabaseAnswer,
  DatabaseQuestion,
  ReportItem,
  SelectedAnswerData,
  TestMentalWellbeingRO,
  UpdateData
} from './interface/test-result.interface';
import { RANGE_MAPPINGS } from '@aos-enum/range-type.enum';

interface AnswerData {
  answerCode: string;
  answerScore: number;
  rag: string;
  redFlag: boolean;
}

@Injectable()
export class CalculatorService {
  private savedNumericValues: Map<string, number> = new Map();

  constructor(
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.TestTimedUpHistory))
    private readonly testTimedUpHistoryModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.TestKccqHistory))
    private readonly testKccqHistoryModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Test))
    private readonly testModel: Model<any>,
    private readonly testService: TestService,
    private readonly flagLoggingService: FlagLoggingService,
  ) { }

  public async calculateMentalWellbeingTest(testResult, user, updateData: UpdateData) {
    await this.processTestData(testResult, updateData, TestType.MENTAL_WELLBEING);

    if (updateData?.cancelled !== true) {
      await this.calculateHADSSubScores(testResult, user);
    } else {
      testResult.totalScores = 0;
      testResult.anxietyScore = 0;
      testResult.depressionScore = 0;
    }
    await this.handleTestStatus(testResult, updateData);
    await testResult.save();
    await this.handlePostTestProcessing(user, testResult, updateData.cancelled === true);
  }

  private async processTestData(testResult, updateData: UpdateData, testType: TestType): Promise<void> {
    const selectedAnswers = [];
    const report: ReportItem[] = [];
    const questions = updateData.detail.questions;

    updateData.detail = testResult.detail;
    updateData.detail.questions = questions;

    const allRequestQuestions = this.getAllQuestionsFromRequest(updateData.detail.questions);
    this.processNumericInputs(allRequestQuestions, testResult.detail.questions);
    this.getSelectedAnswers(questions, testResult.detail.questions, selectedAnswers);

    updateData.detail.questions = this.setSelectedAnswers(
      testResult.detail.questions,
      selectedAnswers,
      report,
      testType,
    );

    testResult.detail = updateData.detail;
    testResult.report = this.sortReportField(report);
    testResult.reds = testResult.greens = testResult.ambers = 0;
  }

  private async handleTestStatus(testResult, updateData: UpdateData): Promise<boolean> {
    if (updateData.completed === true) {
      testResult.completedDate = updateData?.completedDate;
      testResult.status = TestResultStatus.COMPLETED;
      return true;
    } else if (updateData.cancelled === true) {
      testResult.status = TestResultStatus.CANCELLED;
      return false;
    }
    return false;
  }

  private async calculateHADSSubScores(testResult, user?: any): Promise<void> {
    let anxietyScore = 0;
    let depressionScore = 0;

    testResult.detail.questions.forEach((question) => {
      if (question.answers) {
        question.answers.forEach((answer) => {
          if (answer.selected) {
            anxietyScore += answer.anxietyScore || 0;
            depressionScore += answer.depressionScore || 0;
          }
        });
      }
    });

    testResult.anxietyScore = anxietyScore;
    testResult.depressionScore = depressionScore;

    await this.checkMentalWellbeingFlags(testResult, anxietyScore, depressionScore, user);
  }

  private async checkMentalWellbeingFlags(testResult, anxietyScore: number, depressionScore: number, user?: any): Promise<void> {
    if (
      anxietyScore > FlagThreshold.MENTAL_WELLBEING_FLAG_THRESHOLD ||
      depressionScore > FlagThreshold.MENTAL_WELLBEING_FLAG_THRESHOLD
    ) {
      testResult.redFlag = true;

      // Log the mental wellbeing flag with detailed information
      if (user?.username) {
        await this.flagLoggingService.logMentalWellbeingFlag(
          testResult,
          user.username,
          anxietyScore,
          depressionScore,
          FlagThreshold.MENTAL_WELLBEING_FLAG_THRESHOLD
        );
      }
    }
  }

  private async handleCancelledTest(testResult): Promise<void> {
    testResult.status = TestResultStatus.CANCELLED;
  }

  private async handleCompletedTest(testResult, user, updateData): Promise<void> {
    testResult.completedDate = updateData?.completedDate;
    testResult.status = TestResultStatus.COMPLETED;

    const historyRecord = await this.createHistoryRecord(user, testResult, updateData.ttc);
    await this.processHistoryRecord(historyRecord, user, updateData.ttc, testResult);
    await historyRecord.save();
  }

  private async createHistoryRecord(user, testResult, ttc): Promise<any> {
    return new this.testTimedUpHistoryModel({
      patient: user,
      testResult,
      timeScore: ttc,
      redFlag: false,
    });
  }

  private async processHistoryRecord(historyRecord, user, ttc, testResult): Promise<void> {
    const firstRecord = await this.getFirstHistoryRecord(user);

    if (!firstRecord) {
      return;
    }

    const baseLine = firstRecord.timeScore;
    historyRecord.delta = ttc - baseLine;

    if (historyRecord.delta < timedUpPredefinedThreshold) {
      historyRecord.countValue = 0;
    } else {
      await this.calculateCountValue(historyRecord, user);
    }

    this.checkAndSetRedFlag(historyRecord, testResult);
  }

  private async getFirstHistoryRecord(user): Promise<any> {
    return await this.testTimedUpHistoryModel.findOne({ patient: user }).sort({ createdAt: 1 });
  }

  private async calculateCountValue(historyRecord, user): Promise<void> {
    const lastRecord = await this.testTimedUpHistoryModel.findOne({ patient: user }).sort({ createdAt: -1 });

    if (lastRecord.countValue >= 3) {
      historyRecord.countValue = 1;
    } else {
      historyRecord.countValue = lastRecord.countValue + 1;
    }
  }

  private checkAndSetRedFlag(historyRecord, testResult): void {
    if (historyRecord.countValue === 3) {
      historyRecord.redFlag = true;
      testResult.redFlag = true;
    }
  }

  private async handlePostTestProcessing(user, testResult, wasCancelled): Promise<void> {
    if (wasCancelled) {
      await this.testService.createNewTestAfterCancelled(user, testResult);
    } else {
      await this.testService.createNewTestAfterPatientFinishedByType(user, testResult);
    }
  }

  private buildTestTimedUpRO(items): TestMentalWellbeingRO[] {
    const result = [];
    items.forEach((item) => {
      result.push({
        id: item._id,
        completedDate: Date.parse(item.completedDate) / 1000,
        anxietyScore: item.anxietyScore || 0,
        depressionScore: item.depressionScore || 0,
      });
    });
    return result;
  }

  public async calculateSymptomsTest(testResult, user, updateData: UpdateData) {
    await this.processTestData(testResult, updateData, TestType.SYMPTOMS);

    const isCancelled = updateData?.cancelled === true;

    if (!isCancelled) {
      this.calculateSymptomsTestScore(testResult);
      await this.countRedFlagsFromReport(testResult.report, testResult, user);
    } else {
      testResult.totalScores = 0;
    }
    await this.handleSymptomsTestStatus(testResult, updateData, user);
    this.saveNumericValues(testResult);
    await testResult.save();
    await this.handlePostTestProcessing(user, testResult, updateData.cancelled === true);
  }

  private async handleSymptomsTestStatus(testResult, updateData: UpdateData, user): Promise<boolean> {
    if (updateData.completed === true) {
      await this.checkSymptomsTestLevelFlags(testResult, user);
      testResult.completedDate = updateData?.completedDate
      testResult.status = TestResultStatus.COMPLETED;
      return true;
    } else if (updateData.cancelled === true) {
      testResult.status = TestResultStatus.CANCELLED;
      return false;
    }
    return false;
  }

  private async countRedFlagsFromReport(report: ReportItem[], testResult, user?: any): Promise<void> {
    const redAmberQuestions: string[] = [];
    let hasRedAmberFlags = false;

    report.forEach((item) => {
      if (item.redFlag === true) {
        testResult.redFlag = true;
        hasRedAmberFlags = true;
        redAmberQuestions.push(item.code);

        if (item.rag === RAGType.R) {
          testResult.reds++;
        } else if (item.rag === RAGType.A) {
          testResult.ambers++;
        }
      } else if (item.rag === RAGType.G) {
        testResult.greens++;
      }
    });

    // Log Red/Amber flags if any were found
    if (hasRedAmberFlags && user?.username && testResult.type === TestType.SYMPTOMS) {
      const testSubtype = await this.determineSymptomsTestSubtype(testResult);
      const ragType = testResult.reds > 0 ? RAGType.R : RAGType.A;

      await this.flagLoggingService.logRedAmberFlag(
        testResult,
        user.username,
        ragType,
        redAmberQuestions,
        testSubtype
      );
    }
  }

  private processNumericInputs(requestQuestions, databaseQuestions): void {
    if (!Array.isArray(requestQuestions) || requestQuestions.length === 0 ||
        !Array.isArray(databaseQuestions) || databaseQuestions.length === 0) {
      return;
    }

    requestQuestions.forEach((requestQuestion) => {
      this.processNumericQuestion(requestQuestion, databaseQuestions);
      this.processNestedNumericInputs(requestQuestion, databaseQuestions);
    });
  }

  private processNumericQuestion(requestQuestion, databaseQuestions): void {
    if (
      requestQuestion.type === QuestionType.NUMERIC_INPUT &&
      requestQuestion.numericValue !== null &&
      requestQuestion.numericValue !== undefined
    ) {
      const databaseQuestion = this.findQuestionByCode(databaseQuestions, requestQuestion.code);
      this.convertNumericToMultipleChoice(requestQuestion, databaseQuestion);
    }
  }

  private convertNumericToMultipleChoice(requestQuestion, databaseQuestion): void {
    if (databaseQuestion && Array.isArray(databaseQuestion.answers) && databaseQuestion.answers.length > 0) {
      // Save numeric values for specific questions that need them preserved
      if (requestQuestion.code === NumericQuestionCode.HBI_6 || requestQuestion.code === NumericQuestionCode.SCCAI_8) {
        if (requestQuestion.numericValue !== undefined && requestQuestion.numericValue !== null) {
          this.savedNumericValues.set(requestQuestion.code, requestQuestion.numericValue);
        }
      }

      // For numeric questions that have range mappings, convert to multiple choice
      for (const answer of databaseQuestion.answers) {
        // Use the same range resolution logic as isNumericValueInAnswerRange for consistency
        if (this.isNumericValueInAnswerRange(requestQuestion.numericValue, answer)) {
          this.updateQuestionToMultipleChoice(requestQuestion, databaseQuestion, answer);
          break;
        }
      }
    }
  }



  private updateQuestionToMultipleChoice(requestQuestion, databaseQuestion, selectedAnswer): void {
    requestQuestion.type = QuestionType.MULTIPLE_CHOICE_TYPE;
    requestQuestion.answers = databaseQuestion.answers.map((dbAnswer) => ({
      ...dbAnswer,
      selected: dbAnswer.id === selectedAnswer.id,
    }));
  }

  private processNestedNumericInputs(requestQuestion, databaseQuestions): void {
    // Handle direct nested questions (like HBI_6 under HBI_5)
    if (Array.isArray(requestQuestion.questions) && requestQuestion.questions.length > 0) {
      const databaseQuestion = this.findQuestionByCode(databaseQuestions, requestQuestion.code);
      if (databaseQuestion && Array.isArray(databaseQuestion.questions) && databaseQuestion.questions.length > 0) {
        this.processNumericInputs(requestQuestion.questions, databaseQuestion.questions);
      }
    }

    // Handle nested questions under answers (legacy structure)
    if (Array.isArray(requestQuestion.answers) && requestQuestion.answers.length > 0) {
      requestQuestion.answers.forEach((requestAnswer) => {
        if (Array.isArray(requestAnswer.questions) && requestAnswer.questions.length > 0) {
          const databaseAnswer = this.findAnswerById(databaseQuestions, requestAnswer.id);
          if (databaseAnswer && Array.isArray(databaseAnswer.questions) && databaseAnswer.questions.length > 0) {
            this.processNumericInputs(requestAnswer.questions, databaseAnswer.questions);
          }
        }
      });
    }
  }

  private findQuestionByCode(questions: DatabaseQuestion[], code: string): DatabaseQuestion | null {
    if (!Array.isArray(questions) || questions.length === 0) {
      return null;
    }

    for (const question of questions) {
      if (question.code === code) {
        return question;
      }

      // Search in direct nested questions (like HBI_6 under HBI_5)
      if (Array.isArray(question.questions) && question.questions.length > 0) {
        const foundInNestedQuestions = this.findQuestionByCode(question.questions, code);
        if (foundInNestedQuestions) {
          return foundInNestedQuestions;
        }
      }

      // Search in questions under answers (legacy structure)
      const foundInAnswers = this.searchQuestionInAnswers(question.answers, code);
      if (foundInAnswers) {
        return foundInAnswers;
      }
    }
    return null;
  }

  private searchQuestionInAnswers(answers: DatabaseAnswer[], code: string): DatabaseQuestion | null {
    if (!answers) return null;

    for (const answer of answers) {
      if (answer.questions) {
        const found = this.findQuestionByCode(answer.questions, code);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  private findAnswerById(questions: DatabaseQuestion[], answerId: string): DatabaseAnswer | null {
    if (!questions) {
      return null;
    }

    for (const question of questions) {
      if (question.answers) {
        const foundAnswer = this.searchAnswerInQuestion(question.answers, answerId);
        if (foundAnswer) {
          return foundAnswer;
        }
      }
    }
    return null;
  }

  private searchAnswerInQuestion(answers: DatabaseAnswer[], answerId: string): DatabaseAnswer | null {
    for (const answer of answers) {
      if (answer.id === answerId) {
        return answer;
      }

      if (answer.questions) {
        const found = this.findAnswerById(answer.questions, answerId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  private setSelectedAnswers(questions, selectedAnswers, report: ReportItem[], testType?: number) {
    if (!questions) return questions;

    questions.forEach((question) => {
      this.processQuestionForSelection(question, selectedAnswers, report, testType);
    });

    return questions;
  }

  private processQuestionForSelection(question, selectedAnswers, report: ReportItem[], testType?: number): void {
    const answerData = this.initializeAnswerData();

    this.processQuestionAnswers(question, selectedAnswers, answerData, report);

    if (question.type !== QuestionType.CHECKBOX_TYPE) {
      this.addQuestionToReport(question, answerData, report, testType);
    }

    this.processNestedQuestions(question, selectedAnswers, report, testType);
  }

  private processQuestionAnswers(question, selectedAnswers, answerData: AnswerData, report: ReportItem[]): void {
    if (question.answers) {
      question.answers.forEach((answer) => {
        answer.selected = false;
        this.checkSelectedAnswer(answer, selectedAnswers, answerData);
        this.processNestedAnswerQuestions(answer, selectedAnswers, report);
      });

      if (question.type === QuestionType.CHECKBOX_TYPE) {
        this.handleCheckboxReporting(question, selectedAnswers, report);
        return;
      }
    }
  }

  private handleCheckboxReporting(question, selectedAnswers, report: ReportItem[]): void {
    question.answers.forEach((answer) => {
      const isSelected = selectedAnswers.some(element => element.id === answer.id && element.selected);

      if (isSelected) {
        const reportItem: ReportItem = {
          code: `${question.code}_${answer.code}`,
          question: `${question.text} - ${answer.text}`,
          answer: answer.code,
          score: answer.score || 0,
          rag: answer.rag,
          redFlag: answer.rag === RAGType.R || answer.rag === RAGType.A,
          order: question.order,
          sibdqScore: answer.sibdqScore || 0,
          eqScore: answer.eqScore || 0,
        };

        report.push(reportItem);
      }
    });
  }

  private processNestedQuestions(question, selectedAnswers, report: ReportItem[], testType?: number): void {
    if (question.questions) {
      this.setSelectedAnswers(question.questions, selectedAnswers, report, testType);
    }
  }

  private initializeAnswerData(): AnswerData {
    return {
      answerCode: '',
      answerScore: 0,
      rag: '',
      redFlag: false,
    };
  }

  private checkSelectedAnswer(answer, selectedAnswers, answerData: AnswerData): void {
    selectedAnswers.forEach((element) => {
      if (element.id === answer.id) {
        this.updateAnswerData(answer, element, answerData);
      }
    });
  }

  private updateAnswerData(answer, element, answerData: AnswerData): void {
    answer.selected = element.selected;
    answerData.answerCode = answer.code;
    answerData.rag = answer.rag;

    if (answerData.rag === RAGType.R || answerData.rag === RAGType.A) {
      answerData.redFlag = true;
    }

    if (answerData.answerScore === 0 && element.selected) {
      answerData.answerScore = answer.score;
    }

    if (element?.selected && element?.testDate) {
      answer.testDate = element.testDate;
    }

    if (element?.textValue) {
      answer.textValue = element.textValue;
    }

    if (element?.numericValue != null && typeof element?.numericValue === 'number') {
      answer.numericValue = element.numericValue;
    }
  }

  private processNestedAnswerQuestions(answer, selectedAnswers, report: ReportItem[]): void {
    if (answer.questions) {
      answer.questions = this.setSelectedAnswers(answer.questions, selectedAnswers, report);
    }
  }

  private addQuestionToReport(question, answerData: AnswerData, report: ReportItem[], testType?: number): void {
    if (question.code && question.code !== '') {
      const reportItem: ReportItem = {
        code: question.code,
        question: question.text,
        answer: answerData.answerCode,
        score: answerData.answerScore,
        rag: answerData.rag,
        redFlag: answerData.redFlag,
        order: question.order,
      };

      if (question?.code === NumericQuestionCode.HBI_6 || question?.code === NumericQuestionCode.SCCAI_8) {
        if (this.savedNumericValues.has(question.code)) {
          reportItem.numericValue = this.savedNumericValues.get(question.code);
        }
      } else if (question?.code === NumericQuestionCode.GW_12) {
        const selectedAnswer = question?.answers?.find(answer => answer.selected);
        if (selectedAnswer?.numericValue != null && typeof selectedAnswer?.numericValue === 'number') {
          reportItem.numericValue = selectedAnswer?.numericValue;
          reportItem.answer = selectedAnswer?.numericValue?.toString();
        }
      }

      this.addGeneralWellbeingScores(reportItem, question, testType);

      if (question.code === NumericQuestionCode.HBI_5 || question.code === NumericQuestionCode.SCCAI_7) {
        const selectedAnswer = question.answers?.find((answer) => answer.selected && answer.testDate);
        if (selectedAnswer?.testDate) {
          reportItem.testDate = selectedAnswer.testDate;
        }
      }

      this.addMentalWellbeingScores(reportItem, question, testType);

      report.push(reportItem);
    }
  }

  private addMentalWellbeingScores(reportItem: ReportItem, question, testType?: number): void {
    if (testType === TestType.MENTAL_WELLBEING) {
      const selectedAnswer = question.answers?.find((answer) => answer.selected);
      reportItem.anxietyScore = selectedAnswer?.anxietyScore || 0;
      reportItem.depressionScore = selectedAnswer?.depressionScore || 0;
    }
  }

  private addGeneralWellbeingScores(reportItem: ReportItem, question, testType?: number): void {
    if (testType === TestType.GENERAL_WELLBEING) {
      const selectedAnswer = question.answers?.find((answer) => answer.selected);
      if (selectedAnswer?.textValue) {
        reportItem.textValue = selectedAnswer.textValue;
      }

      reportItem.sibdqScore = selectedAnswer?.sibdqScore || 0;
      reportItem.eqScore = selectedAnswer?.eqScore || 0;
    }
  }

  private async getSelectedAnswers(questions, testResult, selectedAnswers) {
    if (!Array.isArray(questions) || questions.length === 0) return;

    questions.forEach((question) => {
      this.processQuestionAnswersForSelection(question, selectedAnswers);
      this.processNestedQuestionsForSelectedAnswers(question, testResult, selectedAnswers);
    });
  }

  private processQuestionAnswersForSelection(question, selectedAnswers): void {
    if (Array.isArray(question.answers) && question.answers.length > 0) {
      question.answers.forEach((answer) => {
        // For NUMERIC_INPUT questions, use special handling to capture numeric values regardless of selected status
        if (question.type === QuestionType.NUMERIC_INPUT) {
          this.checkAndAddNumericAnswer(answer, selectedAnswers, question.testDate, question.textValue, question.numericValue);
        } else {
          this.checkAndAddSelectedAnswer(answer, selectedAnswers, question.testDate, question.textValue, question.numericValue);
        }
        this.processNestedAnswerQuestionsForSelection(answer, selectedAnswers);
      });
    }
  }

  private checkAndAddSelectedAnswer(answer, selectedAnswers, questionTestDate?: string, questionTextValue?: string, questionNumericValue?: number): void {
    if (answer.selected !== undefined && answer.selected !== false) {
      const answerData: SelectedAnswerData = {
        id: answer.id,
        selected: answer.selected,
      };

      // Prioritize answer-level textValue, then fall back to question-level textValue
      if (answer?.textValue) {
        answerData.textValue = answer.textValue;
      } else if (questionTextValue) {
        answerData.textValue = questionTextValue;
      }

      // Prioritize answer-level numericValue, then fall back to question-level numericValue
      if (answer?.numericValue !== undefined && answer?.numericValue !== null) {
        answerData.numericValue = answer.numericValue;
      } else if (questionNumericValue !== undefined && questionNumericValue !== null) {
        answerData.numericValue = questionNumericValue;
      }

      // Prioritize answer-level testDate, then fall back to question-level testDate
      if (answer?.testDate) {
        answerData.testDate = answer.testDate;
      } else if (questionTestDate) {
        answerData.testDate = questionTestDate;
      }

      selectedAnswers.push(answerData);
    }
  }

  private checkAndAddNumericAnswer(answer, selectedAnswers, questionTestDate?: string, questionTextValue?: string, questionNumericValue?: number): void {
    // For NUMERIC_INPUT questions, we need to determine if this answer should be selected
    // based on whether the provided numeric value falls within this answer's range

    // Get the actual numeric value (prioritize answer-level, then question-level)
    let actualNumericValue: number | null = null;
    if (answer?.numericValue !== undefined && answer?.numericValue !== null) {
      actualNumericValue = answer.numericValue;
    } else if (questionNumericValue !== undefined && questionNumericValue !== null) {
      actualNumericValue = questionNumericValue;
    }

    // If no numeric value is provided, skip this answer
    if (actualNumericValue === null) {
      return;
    }

    // Check if this answer's range contains the numeric value
    const shouldBeSelected = this.isNumericValueInAnswerRange(actualNumericValue, answer);

    // Only add to selectedAnswers if this answer should be selected based on the numeric value
    if (shouldBeSelected) {
      const answerData: SelectedAnswerData = {
        id: answer.id,
        selected: true, // CRITICAL: Mark as selected=true for downstream calculations
      };

      // Store the numeric value
      answerData.numericValue = actualNumericValue;

      // Prioritize answer-level textValue, then fall back to question-level textValue
      if (answer?.textValue) {
        answerData.textValue = answer.textValue;
      } else if (questionTextValue) {
        answerData.textValue = questionTextValue;
      }

      // Prioritize answer-level testDate, then fall back to question-level testDate
      if (answer?.testDate) {
        answerData.testDate = answer.testDate;
      } else if (questionTestDate) {
        answerData.testDate = questionTestDate;
      }

      selectedAnswers.push(answerData);
    }
  }

  private isNumericValueInAnswerRange(numericValue: number, answer: any): boolean {
    // Primary: Check if the answer has a direct range property (database approach)
    if (answer?.range && typeof answer.range === 'object') {
      const { min, max } = answer.range;
      return (min === undefined || numericValue >= min) && (max === undefined || numericValue <= max);
    }

    // Fallback: Check RANGE_MAPPINGS for text-based ranges (legacy support)
    if (answer?.text) {
      const range = this.getRangeFromText(answer.text);
      if (range) {
        const { min, max } = range;
        return (min === undefined || numericValue >= min) && (max === undefined || numericValue <= max);
      }
    }

    // If no range is defined, this answer doesn't match the numeric value
    return false;
  }

  private getRangeFromText(text: string): { min: number; max: number } | null {
    if (!text) return null;

    return RANGE_MAPPINGS[text] || null;
  }

  private processNestedAnswerQuestionsForSelection(answer, selectedAnswers): void {
    if (answer.questions) {
      this.getSelectedAnswers(answer.questions, undefined, selectedAnswers);
    }
  }

  private processNestedQuestionsForSelectedAnswers(question, testResult, selectedAnswers): void {
    if (question.questions) {
      this.getSelectedAnswers(question.questions, testResult, selectedAnswers);
    }
  }

  private async checkRedFlagSymptoms(questions, testResult) {
    if (testResult?.type === TestType.GENERAL_WELLBEING) {
      return;
    }

    questions.forEach((question) => {
      this.processQuestionForRedFlags(question, testResult);
    });
  }

  private processQuestionForRedFlags(question, testResult): void {
    if (question.answers) {
      question.answers.forEach((answer) => {
        this.checkAnswerForRedFlag(answer, testResult);
        this.processNestedAnswerQuestionsForRedFlags(answer, testResult);
      });
    }
  }

  private checkAnswerForRedFlag(answer, testResult): void {
    if (answer.selected === true && answer.rag !== undefined) {
      this.updateTestResultForRedFlag(answer.rag, testResult);
    }
  }

  private updateTestResultForRedFlag(rag, testResult): void {
    if (rag === RAGType.R) {
      testResult.redFlag = true;
      testResult.reds++;
    } else if (rag === RAGType.A) {
      testResult.redFlag = true;
      testResult.ambers++;
    } else {
      testResult.greens++;
    }
  }

  private processNestedAnswerQuestionsForRedFlags(answer, testResult): void {
    if (answer.questions) {
      this.checkRedFlagSymptoms(answer.questions, testResult);
    }
  }

  public async calculateGeneralWellbeingTest(testResult, user, updateData: UpdateData): Promise<void> {
    await this.processTestData(testResult, updateData, TestType.GENERAL_WELLBEING);

    if (updateData?.cancelled !== true) {
      this.calculateGeneralWellbeingScore(testResult);
    } else {
      testResult.sibdqScore = 0;
      testResult.eqScore = 0;
      testResult.totalScores = 0;
    }

    testResult.redFlag = false;

    await this.handleTestStatus(testResult, updateData);
    await testResult.save();
    await this.handlePostTestProcessing(user, testResult, updateData.cancelled === true);
  }

  private calculateGeneralWellbeingScore(testResult): void {
    const detail = typeof testResult?.detail === 'string' ? JSON.parse(testResult?.detail) : testResult?.detail;

    let totalSibdqScore = 0;
    let eqScoreString = '';

    detail.questions.forEach((question) => {
      const selectedAnswer = question?.answers?.find(answer => answer.selected);
      if (selectedAnswer) {
        if (selectedAnswer?.sibdqScore !== null && selectedAnswer?.sibdqScore !== undefined) {
          totalSibdqScore += selectedAnswer?.sibdqScore;
        }

        if (selectedAnswer?.eqScore !== null && selectedAnswer?.eqScore !== undefined && selectedAnswer?.eqScore !== 0) {
          eqScoreString += selectedAnswer?.eqScore?.toString();
        }
      }
    });

    testResult.sibdqScore = totalSibdqScore;

    testResult.eqScore = eqScoreString;
  }



  private sortReportField(report): ReportItem[] {
    return report.sort((a, b) => {
      if (a.order < b.order) {
        return -1;
      }

      return a.order > b.order ? 1 : 0;
    });
  }

  private calculateSymptomsTestScore(testResult): void {
    testResult.totalScores = testResult.detail.questions
      .flatMap((question) => question.answers)
      .filter((answer) => answer.selected)
      .reduce((sum, answer) => sum + (answer.score || 0), 0);
  }

  private async checkSymptomsTestLevelFlags(testResult, user): Promise<void> {
    const testSubtype = await this.determineSymptomsTestSubtype(testResult);
    const prevTests = await this.getPreviousTests(user, testResult);

    if (prevTests.length === 0) return;

    const flagThresholds = this.getFlagThresholds(testSubtype);
    const currentScore = testResult.totalScores;
    const prevScore = prevTests[0].totalScores || 0;
    const scoreDiff = currentScore - prevScore;

    await this.checkSingleIncreaseFlag(testResult, scoreDiff, flagThresholds.singleThreshold, user, testSubtype, currentScore, prevScore);
    await this.checkSustainedIncreaseFlag(testResult, scoreDiff, prevTests, flagThresholds.sustainedThreshold, user, testSubtype, currentScore);
  }

  private async getPreviousTests(user, testResult): Promise<any[]> {
    return await this.testResultModel
      .find({
        patient: user._id,
        type: TestType.SYMPTOMS,
        status: TestResultStatus.COMPLETED,
        _id: { $ne: testResult._id },
      })
      .sort({ completedDate: -1 })
      .limit(2);
  }

  private getFlagThresholds(testSubtype: SymptomsTestSubtype): { singleThreshold: number; sustainedThreshold: number } {
    return {
      singleThreshold:
        testSubtype === SymptomsTestSubtype.HBI
          ? FlagThreshold.HBI_SINGLE_INCREASE
          : FlagThreshold.SCCAI_SINGLE_INCREASE,
      sustainedThreshold:
        testSubtype === SymptomsTestSubtype.HBI
          ? FlagThreshold.HBI_SUSTAINED_INCREASE
          : FlagThreshold.SCCAI_SUSTAINED_INCREASE,
    };
  }

  private async checkSingleIncreaseFlag(
    testResult,
    scoreDiff: number,
    singleThreshold: number,
    user: any,
    testSubtype: SymptomsTestSubtype,
    currentScore: number,
    prevScore: number
  ): Promise<void> {
    if (scoreDiff >= singleThreshold) {
      testResult.redFlag = true;

      // Log the score increase flag
      if (user?.username) {
        await this.flagLoggingService.logScoreIncreaseFlag(
          testResult,
          user.username,
          scoreDiff,
          currentScore,
          prevScore,
          testSubtype,
          singleThreshold
        );
      }
    }
  }

  private async checkSustainedIncreaseFlag(
    testResult,
    scoreDiff: number,
    prevTests: any[],
    sustainedThreshold: number,
    user: any,
    testSubtype: SymptomsTestSubtype,
    currentScore: number,
  ): Promise<void> {
    if (prevTests.length >= 2) {
      const prevPrevScore = prevTests[1].totalScores || 0;
      const prevScore = prevTests[0].totalScores || 0;
      const prevScoreDiff = prevScore - prevPrevScore;

      if (scoreDiff >= sustainedThreshold && prevScoreDiff >= sustainedThreshold) {
        testResult.redFlag = true;

        // Log the sustained increase flag
        if (user?.username) {
          await this.flagLoggingService.logSustainedIncreaseFlag(
            testResult,
            user.username,
            scoreDiff,
            currentScore,
            prevScore,
            testSubtype,
            sustainedThreshold
          );
        }
      }
    }
  }

  private async determineSymptomsTestSubtype(testResult): Promise<SymptomsTestSubtype> {
    const test = await this.testModel.findById(testResult.test);
    if (test?.ibdSubtype === IBDSubtype.CROHNS_DISEASE) {
      return SymptomsTestSubtype.HBI;
    }
    if (test?.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS) {
      return SymptomsTestSubtype.SCCAI;
    }
    return SymptomsTestSubtype.HBI;
  }

  private saveNumericValues(testResult): void {
    testResult.report.forEach((reportItem) => {
      if (this.savedNumericValues.has(reportItem.code)) {
        const numericValue = this.savedNumericValues.get(reportItem.code);
        reportItem.numericValue = numericValue;
      }
    });
    this.savedNumericValues.clear();
  }

  private getAllQuestionsFromRequest(questions: any[]): any[] {
    const allQuestions = [];

    if (!questions) return allQuestions;

    for (const question of questions) {
      allQuestions.push(question);
      allQuestions.push(...this.getNestedQuestionsFromQuestion(question));
    }

    return allQuestions;
  }

  private getNestedQuestionsFromQuestion(question: any): any[] {
    const nestedQuestions = [];

    // Add nested questions
    if (question.questions) {
      nestedQuestions.push(...this.getAllQuestionsFromRequest(question.questions));
    }

    // Add questions in answers
    if (question.answers) {
      for (const answer of question.answers) {
        if (answer.questions) {
          nestedQuestions.push(...this.getAllQuestionsFromRequest(answer.questions));
        }
      }
    }

    return nestedQuestions;
  }
}
