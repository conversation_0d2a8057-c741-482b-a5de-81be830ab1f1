import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LoggerModule } from '../../logger/logger.module';
import { FirebaseService } from '../../shared/services/firebase.service';


import { DatabaseModule } from '@aos-database/database.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { EmailService } from '../../shared/services/email.service';
import { EmailRetryService } from '../../shared/services/email-retry.service';
import { SharedModule } from '../../shared/shared.module';
import { UserService } from '../user/user.service';
import { PatientController } from './patient.controller';
import { PatientService } from './patient.service';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
    LoggerModule.forRoot(),
  ],
  providers: [PatientService, FirebaseService, UserService, EmailService, EmailRetryService],
  controllers: [PatientController],
  exports: [PassportModule],
})
export class PatientModule {}
