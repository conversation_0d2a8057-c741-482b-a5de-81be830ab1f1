import { UserRole } from '@aos-enum/user-role.enum';

export interface GetPatientsRO {
  total: number;
  limit: number;
  page: number;
  users: GetPatientRO[];
}

export interface GetPatientRO {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  role: UserRole;
  isArchived: boolean;
  archiveReason: string;
  nhsNumber: string;
  revealPassword: string;
  dateOfBirth: Date;
  ibdSubtype: string;
  age?: number;
  uuid?: string;
  ethnicity?: string;
  smokingStatus?: string;
  yearOfDiagnosis?: Date;
  expectedDueDate?: Date;
  parity?: string;
  currentMeds?: string;
  lastDiseaseActivity?: Date;
  previousMeds?: string;
  medicalHistory?: string;
  lastFcpDate?: Date;
  lastFcpResult?: string;
  emailRetryCount?: number;
  lastEmailAttempt?: Date;
}
