import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { IsEmailAlreadyExist } from '@aos-validator/is-email-already-exist.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsDefined, IsEmail, IsEnum, IsOptional, Matches, MaxLength, ValidateIf, IsString, IsIn } from 'class-validator';

export class CreatePatientDto {
  @ApiProperty({
    required: false,
    description: 'Email Address (optional)',
  })
  @IsOptional()
  @IsEmail({}, {
    message: 'Email must be a valid email address',
  })
  @MaxLength(150, {
    message: 'Email cannot exceed 150 characters',
  })
  @IsEmailAlreadyExist({
    message: 'Email $value already exists. Choose another email.',
  })
  @ValidateIf((_object, value) => !!value)
  readonly email?: string;

  @ApiProperty()
  @IsDefined({
    message: 'First Name is required',
  })
  @Matches(/^[a-z-' ]+$/i, {
    message: 'First Name can only contain alphabetical characters',
  })
  @MaxLength(50, {
    message: 'First names cannot exceed 50 characters.',
  })
  readonly firstName: string;

  @ApiProperty()
  @IsDefined({
    message: 'Last Name is required',
  })
  @Matches(/^[a-z-' ]+$/i, {
    message: 'Last Name can only contain alphabetical characters',
  })
  @MaxLength(50, {
    message: 'Surnames cannot exceed 50 characters.',
  })
  readonly lastName: string;

  @ApiProperty()
  @IsDefined({
    message: 'Mobile Number is required',
  })
  @Matches(/^07\d{9}$/, {
    message: 'Mobile number must be 11 digits and start with 07',
  })
  mobileNumber: string;

  @ApiProperty()
  @IsDefined({
    message: 'NHS Number is required',
  })
  @Matches(/^\d{10}$/, {
    message: 'NHS number must be exactly 10 digits',
  })
  nhsNumber: string;

  @ApiProperty({
    required: false,
  })
  @IsDefined({
    message: 'dateOfBirth is required',
  })
  dateOfBirth: Date;

  @ApiProperty({
    required: true,
    description: 'Expected Due Date is required for pregnancy patients',
  })
  @IsDefined({
    message: 'Expected Due Date is required for pregnancy patients',
  })
  @IsDateString()
  expectedDueDate: string;

  @ApiProperty({
    required: true,
    description: 'IBD Subtype is required',
    enum: IBDSubtype,
  })
  @IsDefined({
    message: 'IBD Subtype is required',
  })
  @IsEnum(IBDSubtype, {
    message: 'IBD Subtype must be either CROHNS_DISEASE or ULCERATIVE_COLITIS',
  })
  ibdSubtype: IBDSubtype;

  // New mandatory fields
  @ApiProperty({
    required: true,
    description: 'Ethnicity is required',
    enum: ['White', 'Mixed/Multiple ethnic groups', 'Asian/Asian British', 'Black, Black British, Caribbean or African', 'Other'],
  })
  @IsDefined({
    message: 'Ethnicity is required',
  })
  @IsIn(['White', 'Mixed/Multiple ethnic groups', 'Asian/Asian British', 'Black, Black British, Caribbean or African', 'Other'], {
    message: 'Ethnicity must be one of the predefined options',
  })
  ethnicity: string;

  @ApiProperty({
    required: true,
    description: 'Smoking Status is required',
    enum: ['None', 'Rarely (monthly)', 'Socially (weekly)', 'Trying to quit', 'Often (daily)', 'Heavily (daily)'],
  })
  @IsDefined({
    message: 'Smoking Status is required',
  })
  @IsIn(['None', 'Rarely (monthly)', 'Socially (weekly)', 'Trying to quit', 'Often (daily)', 'Heavily (daily)'], {
    message: 'Smoking Status must be one of the predefined options',
  })
  smokingStatus: string;

  @ApiProperty({
    required: true,
    description: 'Year of Diagnosis is required',
  })
  @IsDefined({
    message: 'Year of Diagnosis is required',
  })
  @IsDateString()
  yearOfDiagnosis: string;

  @ApiProperty({
    required: true,
    description: 'Parity is required',
    enum: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '8+'],
  })
  @IsDefined({
    message: 'Parity is required',
  })
  @IsIn(['0', '1', '2', '3', '4', '5', '6', '7', '8', '8+'], {
    message: 'Parity must be one of the predefined options',
  })
  parity: string;

  @ApiProperty({
    required: true,
    description: 'Medical History/Comorbidities is required',
  })
  @IsDefined({
    message: 'Medical History/Comorbidities is required',
  })
  @IsString()
  @MaxLength(600, {
    message: 'Medical History cannot exceed 600 characters',
  })
  medicalHistory: string;

  // Optional fields
  @ApiProperty({
    required: false,
    description: 'UUID for iPhone users',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50, {
    message: 'UUID cannot exceed 50 characters',
  })
  uuid?: string;

  @ApiProperty({
    required: false,
    description: 'Current Medications',
  })
  @IsOptional()
  @IsString()
  @MaxLength(600, {
    message: 'Current Medications cannot exceed 600 characters',
  })
  currentMeds?: string;

  @ApiProperty({
    required: false,
    description: 'Last Disease Activity date',
  })
  @IsOptional()
  @IsDateString()
  lastDiseaseActivity?: string;

  @ApiProperty({
    required: false,
    description: 'Previous Medications',
  })
  @IsOptional()
  @IsString()
  @MaxLength(600, {
    message: 'Previous Medications cannot exceed 600 characters',
  })
  previousMeds?: string;

  @ApiProperty({
    required: false,
    description: 'Last FCP Date',
  })
  @IsOptional()
  @IsDateString()
  lastFcpDate?: string;

  @ApiProperty({
    required: false,
    description: 'Last FCP Result',
  })
  @IsOptional()
  @IsString()
  @MaxLength(50, {
    message: 'Last FCP Result cannot exceed 50 characters',
  })
  lastFcpResult?: string;
}
