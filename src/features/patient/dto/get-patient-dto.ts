import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsPositive, IsString, Min } from 'class-validator';

export class GetPatientDto {
  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  limit: number = 10;

  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page: number = 1;

  @ApiProperty({
    required: false,
    default: 'lastName',
  })
  @IsOptional()
  @IsString()
  sortBy: string = 'lastName';

  @ApiProperty({
    required: false,
    description: 'Sort strategy example: asc | desc | 1 | -1',
    example: 'asc',
    default: 'asc',
  })
  @IsOptional()
  @IsString()
  sortType: string = 'asc';

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isArchived: boolean;

  @ApiProperty({
    required: false,
    description: 'Search by first name, last name and NHS Number (patient only)',
  })
  @IsOptional()
  @IsString()
  searchText: string;
}
