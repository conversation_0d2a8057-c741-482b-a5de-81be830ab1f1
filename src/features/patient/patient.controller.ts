import { Permissions } from '@aos-decorator/permission.decorator';
import { UserRole } from '@aos-enum/user-role.enum';
import { PermissionGuard } from '@aos-guard/permission.guard';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { Body, Controller, Get, HttpStatus, Param, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { CreatePatientDto, UpdateIBDSubtypeDto } from './dto';
import { GetPatientDto } from './dto/get-patient-dto';
import { CreatePatientRO } from './interface/create-patient.interface';
import { GetPatientRO, GetPatientsRO } from './interface/get-patient.ro';
import { PatientService } from './patient.service';

@Controller('patient')
@ApiTags('patient')
@ApiBearerAuth()
export class PatientController {
  constructor(private readonly patientService: PatientService) {}

  @ApiOperation({ description: ' Create User ' })
  @Post()
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  async createUser(@Body() createUserDto: CreatePatientDto): Promise<CreatePatientRO> {
    return await this.patientService.createUser(createUserDto);
  }

  @ApiOperation({ description: ' Get Patient by query and pagination' })
  @Get()
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  public async getUsersPagination(@Query() getUserDto: GetPatientDto): Promise<GetPatientsRO> {
    return await this.patientService.getUserByPagination(getUserDto);
  }

  @ApiOperation({ description: 'Get app download link' })
  @Get('app-download')
  async getAppDownloadLink(@Query('token') token: string, @Req() req: any, @Res() res: Response): Promise<any> {
    const userAgent = req.headers['user-agent'] || '';
    const result = await this.patientService.validateAndGetAppDownloadLink(token, userAgent);

    return res.redirect(HttpStatus.FOUND, result.downloadUrl);
  }

  @ApiOperation({ description: ' View User by Detail' })
  @Get(':id')
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  public async getUserById(@Param('id') id): Promise<GetPatientRO> {
    return await this.patientService.getUserById(id);
  }

  @ApiOperation({ description: ' Update IBD Subtype for Patient' })
  @Put(':id/ibd-subtype')
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  public async updateIBDSubtype(
    @Param('id') patientId: string,
    @Body() updateIBDSubtypeDto: UpdateIBDSubtypeDto,
  ): Promise<void> {
    return await this.patientService.updateIBDSubtype(patientId, updateIBDSubtypeDto);
  }
}
