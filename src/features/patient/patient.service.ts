import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';

import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { UserRole } from '@aos-enum/user-role.enum';
import { sendMail } from '@aos-shared/helpers/node-mailer.helper';
import StringHelper from '@aos-shared/helpers/string.helper';
import { EmailRetryService } from '@aos-shared/services/email-retry.service';
import * as moment from 'moment';
import * as RandExp from 'randexp';
import { v4 as uuidv4 } from 'uuid';
import { baseUrl, deepLinkAppStore, deepLinkGoogleStore } from '../../app.config';
import { MESSAGES } from '../../shared/constants/error-messages.constant';
import { CreatePatientDto, UpdateIBDSubtypeDto } from './dto';
import { GetPatientDto } from './dto/get-patient-dto';
import { CreatePatientRO } from './interface/create-patient.interface';
import { GetPatientRO, GetPatientsRO } from './interface/get-patient.ro';
import fs = require('fs');

@Injectable()
export class PatientService {
  private readonly logger = new Logger(PatientService.name);
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    private readonly emailRetryService: EmailRetryService,
  ) {}

  async createUser(createUserDto: CreatePatientDto): Promise<CreatePatientRO> {
    const existingUsers = await this.userModel.find({}, 'username');
    const existingUsernames = existingUsers.map((user) => user.username);

    const generatedUsername = StringHelper.generateUsername(
      createUserDto.firstName,
      createUserDto.lastName,
      existingUsernames,
    );

    // Calculate age from date of birth
    const dateOfBirth = moment(createUserDto.dateOfBirth, 'DD/MM/YYYY').utc().toDate();
    const age = moment().diff(moment(dateOfBirth), 'years');

    const createdUser = new this.userModel({
      ...createUserDto,
      username: generatedUsername,
      role: UserRole.PATIENT,
      dateOfBirth,
      age,
    });

    // Handle optional date fields
    if (createUserDto.expectedDueDate) {
      createdUser.expectedDueDate = moment(createUserDto.expectedDueDate).utc().toDate();
    }

    if (createUserDto.yearOfDiagnosis) {
      createdUser.yearOfDiagnosis = moment(createUserDto.yearOfDiagnosis).utc().toDate();
    }

    if (createUserDto.lastDiseaseActivity) {
      createdUser.lastDiseaseActivity = moment(createUserDto.lastDiseaseActivity).utc().toDate();
    }

    if (createUserDto.lastFcpDate) {
      createdUser.lastFcpDate = moment(createUserDto.lastFcpDate).utc().toDate();
    }

    const generatedPassword = StringHelper.generatePassword();
    const appDownloadToken = uuidv4();

    try {
      createdUser.password = generatedPassword;
      createdUser.revealPassword = generatedPassword;
      createdUser.appDownloadToken = appDownloadToken;
      createdUser.emailRetryCount = 0;
      await createdUser.save();
    } catch (ex) {
      console.log('🚀 ~ PatientService ~ createUser ~ ex:', ex);
      this.logger.error(`Failed to create patient account for ${createdUser.username} due to ${ex.message}`);
      throw new BadRequestException(MESSAGES.ERROR_ON_SAVING);
    }

    // Send email with retry logic (only if email is provided)
    if (createdUser.email) {
      await this.sendEmailToPatientWithRetry(createdUser);
    }

    return {
      username: createdUser.username,
    };
  }

  async getUserByPagination(getUserDto: GetPatientDto): Promise<GetPatientsRO> {
    const query = {
      $and: [
        PatientService.buildIsArchiveQuery(getUserDto),
        PatientService.buildSearchQuery(getUserDto),
      ]
    };
    const sortBy = PatientService.buildSortBy(getUserDto);
    const roleQuery = { role: { $in: [UserRole.PATIENT, UserRole.DEMO] } };

    const finalQuery = {
      ...roleQuery,
      ...query,
    };

    const total = await this.userModel.find(finalQuery).countDocuments();
    const limit = +getUserDto.limit <= 0 ? total : +getUserDto.limit;
    const page = +getUserDto.page;
    const users = await this.userModel
      .find(finalQuery)
      .skip((page - 1) * limit)
      .limit(limit)
      .collation({ locale: 'en' })
      .sort({ ...sortBy });
    const usersRO = this.buildGetUsersRO(users);
    return {
      limit,
      page,
      total,
      users: usersRO,
    };
  }

  async getUserById(id: string): Promise<GetPatientRO> {
    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException(MESSAGES.PATIENT_USERNAME_EXISTS);
    }
    return PatientService.buildGetUserRO(user);
  }

  private buildGetUsersRO(users): GetPatientRO[] {
    const items: GetPatientRO[] = [];
    users.forEach((entry) => items.push(PatientService.buildGetUserRO(entry)));
    return items;
  }

  private static buildGetUserRO(user): GetPatientRO {
    if (!user) {
      return null;
    }
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      mobileNumber: user.mobileNumber,
      role: user.role,
      isArchived: user.isArchived,
      archiveReason: user.archiveReason,
      nhsNumber: user.nhsNumber,
      dateOfBirth: user.dateOfBirth,
      revealPassword: user.revealPassword,
      ibdSubtype: user.ibdSubtype,
      age: user.age,
      uuid: user.uuid,
      ethnicity: user.ethnicity,
      smokingStatus: user.smokingStatus,
      yearOfDiagnosis: user.yearOfDiagnosis,
      expectedDueDate: user.expectedDueDate,
      parity: user.parity,
      currentMeds: user.currentMeds,
      lastDiseaseActivity: user.lastDiseaseActivity,
      previousMeds: user.previousMeds,
      medicalHistory: user.medicalHistory,
      lastFcpDate: user.lastFcpDate,
      lastFcpResult: user.lastFcpResult,
      emailRetryCount: user.emailRetryCount,
      lastEmailAttempt: user.lastEmailAttempt,
    };
  }

  private static buildIsArchiveQuery(getUserDto: GetPatientDto) {
    if (getUserDto.isArchived === undefined) {
      return {};
    }

    if (getUserDto.isArchived === false) {
      // When looking for non-archived users, include users where isArchived is false, null, or undefined
      return {
        $or: [{ isArchived: false }, { isArchived: { $exists: false } }, { isArchived: null }],
      };
    }

    return {
      isArchived: getUserDto.isArchived,
    };
  }

  private static buildSortBy(getUserDto: GetPatientDto) {
    if (!getUserDto.sortBy || !getUserDto.sortType) {
      return {};
    }
    const sort = {};
    sort[getUserDto.sortBy] = getUserDto.sortType;
    return sort;
  }

  private static buildSearchQuery(getUserDto: GetPatientDto) {
    const { searchText } = getUserDto;
    if (!searchText) {
      return {};
    }
    const searchRegex = new RegExp(searchText.replace(/[[\]{}()*+?.,\\/^$|#\s]/g, '\\$&'));
    const searchQuery = [
      {
        firstName: {
          $regex: searchRegex,
          $options: 'i',
        },
      },
      {
        lastName: {
          $regex: searchRegex,
          $options: 'i',
        },
      },
    ];
    return {
      $or: [
        ...searchQuery,
        {
          nhsNumber: {
            $regex: searchRegex,
            $options: 'i',
          },
        },
        {
          username: {
            $regex: searchRegex,
            $options: 'i',
          },
        },
      ],
    };
  }

  async sendEmailToPatientWithRetry(user): Promise<void> {
    try {
      let html = fs.readFileSync(`./src/assets/email-templates/patient-password.html`, 'utf-8');

      const appDownloadLink = `${baseUrl}/api/v1/patient/app-download?token=${user.appDownloadToken}`;

      html = html
        .replace('#FirstName#', `${user.firstName}`)
        .replace('#LastName#', `${user.lastName}`)
        .replace('#UserName#', `${user.username}`)
        .replace('#Password#', `${user.revealPassword}`)
        .replace('#AppDownloadLink#', appDownloadLink);

      // Send email with retry logic
      const emailSent = await this.sendEmailsWithRetry(user.email, html);

      // Update user record with email attempt info
      await this.userModel.findByIdAndUpdate(user._id, {
        $set: {
          lastEmailAttempt: moment().utc().toDate(),
          emailRetryCount: emailSent ? 0 : 3, // Set to 3 if failed after all retries
        },
      });
    } catch (ex) {
      this.logger.error(`Cannot send email: ${ex}`);
      // Update user record to indicate email failure
      await this.userModel.findByIdAndUpdate(user._id, {
        $set: {
          lastEmailAttempt: moment().utc().toDate(),
          emailRetryCount: 3,
        },
      });
    }
  }

  async sendEmailToPatient(user): Promise<void> {
    try {
      let html = fs.readFileSync(`./src/assets/email-templates/patient-password.html`, 'utf-8');

      const appDownloadLink = `${baseUrl}/api/v1/patient/app-download?token=${user.appDownloadToken}`;

      html = html
        .replace('#FirstName#', `${user.firstName}`)
        .replace('#LastName#', `${user.lastName}`)
        .replace('#UserName#', `${user.username}`)
        .replace('#Password#', `${user.revealPassword}`)
        .replace('#AppDownloadLink#', appDownloadLink);
      await this.sendEmails(user.email, html);
    } catch (ex) {
      this.logger.error(`Cannot send email: ${ex}`);
    }
  }

  async sendEmailsWithRetry(patientEmail: string, content: string): Promise<boolean> {
    const additionalEmails = [];
    additionalEmails.unshift(patientEmail);

    const results = await this.emailRetryService.sendEmailsWithRetry(
      additionalEmails,
      MESSAGES.WELCOME_TO_IBD_PRAM,
      content,
      {
        maxRetries: 3,
        retryIntervalMinutes: 5,
      },
    );

    if (results.failed.length > 0) {
      this.logger.error(`Failed to send emails to: ${results.failed.join(', ')}`);
      return false;
    }

    this.logger.log(`Successfully sent emails to: ${results.successful.join(', ')}`);
    return true;
  }

  async sendEmails(patientEmail, content): Promise<void> {
    const additionalEmails = [];
    additionalEmails.unshift(patientEmail);

    additionalEmails.forEach((email) => {
      sendMail(email, MESSAGES.WELCOME_TO_IBD_PRAM, content).catch((error) => {
        this.logger.error(`Failed to send email to ${email}: ${error}`);
        return null;
      });
    });
  }

  async validateAndGetAppDownloadLink(token: string, userAgent: string): Promise<any> {
    const user = await this.userModel.findOne({
      appDownloadToken: token,
    });

    if (!user) {
      throw new BadRequestException(MESSAGES.INVALID_DOWNLOAD_LINK);
    }

    const tokenExpiry = moment(user.createdAt).add(24, 'hours');
    if (moment().isAfter(tokenExpiry)) {
      throw new BadRequestException(MESSAGES.DOWNLOAD_LINK_EXPIRED);
    }

    const isAndroid = /Android/.test(userAgent);

    let downloadUrl = '';
    if (isAndroid) {
      downloadUrl = deepLinkGoogleStore;
    } else {
      downloadUrl = deepLinkAppStore;
    }

    return {
      downloadUrl,
      username: user.username,
      password: user.revealPassword,
    };
  }

  async updateIBDSubtype(patientId: string, updateIBDSubtypeDto: UpdateIBDSubtypeDto): Promise<void> {
    const patient = await this.userModel.findById(patientId);

    if (!patient) {
      throw new NotFoundException('Patient not found');
    }

    if (patient.role !== UserRole.PATIENT) {
      throw new BadRequestException('Only patient users can have IBD subtype');
    }

    try {
      await this.userModel.findByIdAndUpdate(patientId, {
        $set: {
          ibdSubtype: updateIBDSubtypeDto.ibdSubtype,
        },
      });
    } catch (ex) {
      throw new BadRequestException('Error updating IBD subtype');
    }
  }
}
