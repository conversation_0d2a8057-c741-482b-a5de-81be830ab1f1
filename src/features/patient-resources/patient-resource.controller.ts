import { Permissions } from '@aos-decorator/permission.decorator';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserRole } from './../../shared/enums/user-role.enum';
import { PermissionGuard } from './../../shared/guards/permission.guard';
import { PatientResourceRO } from './interface/patient-resource.interface';
import { PatientResourceService } from './patient-resource.service';

@ApiBearerAuth()
@ApiTags('patient-resources')
@Controller('patient-resources')
export class PatientResourceController {
  constructor(private readonly patientResourceService: PatientResourceService) {}

  @ApiOperation({ description: ' Get patient resources ' })
  @Get()
  @Permissions(UserRole.PATIENT, UserRole.DEMO)
  @UseGuards(AuthGuard, PermissionGuard)
  async findAll(): Promise<PatientResourceRO[]> {
    return await this.patientResourceService.findAll();
  }
}
