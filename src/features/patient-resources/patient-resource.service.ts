import { Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { Model } from 'mongoose';
import { PatientResourceRO } from './interface/patient-resource.interface';

import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { MESSAGES } from '@aos-shared/constants/error-messages.constant';

@Injectable()
export class PatientResourceService {
  constructor(
    @Inject(getCollectionToken(AosCollections.PatientResource))
    private readonly patientResourceModel: Model<any>,
  ) {}

  async findAll(): Promise<PatientResourceRO[]> {
    try {
      const items = await this.patientResourceModel.find();
      const result = [];

      for (const item of items) {
        result.push(this.buildPatientResourceModel(item));
      }

      return result;
    } catch (error) {
      throw new InternalServerErrorException(MESSAGES.PATIENT_RESOURCES_LOAD_FAILED);
    }
  }

  private buildPatientResourceModel(record: PatientResourceRO): PatientResourceRO {
    return {
      id: record.id,
      title: record.title,
      link: record.link,
      createdAt: Date.parse(record.createdAt.toString()) / 1000,
    };
  }
}
