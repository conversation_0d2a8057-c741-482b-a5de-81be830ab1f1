import { UserRole } from '@aos-enum/user-role.enum';

export interface GetUsersRO {
  total: number;
  limit: number;
  page: number;
  users: GetUserRO[];
}

export interface GetUserRO {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  designation: string;
  site: string;
  mobileNumber: string;
  role: UserRole;
  isArchived: boolean;
  archiveReason: string;
  nhsNumber: string;
}
