import { UserStatus } from '@aos-enum/user-status.enum';
import { BadRequestException, ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IUserModel } from '../../database/types/model.types';

import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { UserRole } from '@aos-enum/user-role.enum';
import { MESSAGES } from '@aos-shared/constants/error-messages.constant';
import { sendMail } from '@aos-shared/helpers/node-mailer.helper';
import * as bcrypt from 'bcrypt';
import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { clinicianPortalUrl } from '../../app.config';
import { ResetPasswordDto } from '../auth/dto/reset-password.dto';
import { CreateUserDto } from './dto';
import { ArchiveUserDto } from './dto/archive-user.dto';
import { GetUserDto } from './dto/get-user-dto';
import { RequestResetPasswordTokenDto } from './dto/request-reset-password-token.dto';
import { VerifyResetPasswordTokenDto } from './dto/verify-reset-password-token.dto';
import { ArchiveUserRO } from './interface/archive-user.ro';
import { CreateUserInterface } from './interface/create-user.interface';
import { GetUserRO, GetUsersRO } from './interface/get-user.ro';
import { RequestResetPasswordTokenRO } from './interface/request-reset-password-token.interface';
import { ResetPasswordRO } from './interface/reset-password.interface';
import { VerifyResetPasswordTokenRO } from './interface/verify-reset-password-token.interface';
import fs = require('fs');

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: IUserModel,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<CreateUserInterface> {
    const createdUser = new this.userModel({
      ...createUserDto,
    });

    const username = await this.generateUsernameIfExist(createUserDto.firstName, createUserDto.lastName);

    try {
      createdUser.username = username;
      await createdUser.save();
    } catch (ex) {
      throw new BadRequestException(MESSAGES.ERROR_ON_SAVING);
    }

    await this.requestResetPasswordToken({
      email: createUserDto.email,
    });

    return {
      email: createdUser.username,
    };
  }

  async checkUserExist(username: string): Promise<boolean> {
    const user = await this.userModel.findOne({
      username,
    });
    return !!user;
  }

  async generateUsernameIfExist(firstName, lastName): Promise<string> {
    firstName = firstName.toLowerCase().replace(/\s/g, '');
    lastName = lastName.toLowerCase().replace(/\s/g, '');
    let username = `${firstName}${lastName}`;
    let randomPrefix = await this.countUserByUsername(username);
    randomPrefix = !randomPrefix ? 1 : randomPrefix;
    while (await this.checkUserExist(`${username}`)) {
      username = `${firstName}${lastName}${randomPrefix}`;
      randomPrefix++;
    }
    return username;
  }

  async requestResetPasswordToken(
    requestResetPasswordTokenDto: RequestResetPasswordTokenDto,
  ): Promise<RequestResetPasswordTokenRO> {
    const userToAttempt = await this.userModel.findOne({ email: requestResetPasswordTokenDto.email }).exec();

    if (!userToAttempt) {
      throw new NotFoundException(`Email ${requestResetPasswordTokenDto.email} does not exists`);
    }

    if (userToAttempt.role === UserRole.PATIENT) {
      throw new BadRequestException('Patient cannot reset the password');
    }

    const action = userToAttempt.password ? 'FORGOT_PASSWORD' : 'CREATE_NEW_PASSWORD';

    userToAttempt.resetPasswordToken = uuidv4();
    userToAttempt.requestResetPasswordDate = moment.utc().format();
    await this.userModel.updateOne(
      { _id: userToAttempt._id },
      {
        $set: {
          resetPasswordToken: userToAttempt.resetPasswordToken,
          requestResetPasswordDate: userToAttempt.requestResetPasswordDate,
        },
      },
    );

    await this.sendEmailResetPassword(userToAttempt);
    return {
      username: userToAttempt.username,
      action,
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<ResetPasswordRO> {
    const user = await this.userModel
      .findOne({
        resetPasswordToken: resetPasswordDto.token,
      })
      .exec();

    // Check if this is first-time password setup (user had no password before)
    const isFirstTimePasswordSetup = !user.password;
    const isClinicianOrAdmin = user.role === UserRole.CLINICIAN || user.role === UserRole.SUPER_ADMIN;

    const salt = bcrypt.genSaltSync(10);
    user.password = bcrypt.hashSync(resetPasswordDto.password, salt);

    await this.userModel.updateOne(
      { _id: user._id },
      {
        $unset: {
          resetPasswordToken: '',
          requestResetPasswordDate: '',
        },
        $set: { password: user.password },
      },
    );
    if (user.role === UserRole.PATIENT) {
      await this.setRevealPassword(user._id, resetPasswordDto.password);
    }

    // Send clinician onboarding email after first-time password setup completion
    if (isFirstTimePasswordSetup && isClinicianOrAdmin && user.email) {
      await this.sendClinicianOnboardingEmail(user);
    }

    return {
      username: user.username,
    };
  }

  async countUserByUsername(username: string): Promise<number> {
    return await this.userModel
      .find({
        username: {
          $regex: username,
          $options: 'i',
        },
      })
      .countDocuments();
  }

  async sendEmailResetPassword(user): Promise<void> {
    try {
      const isCreateNew = !user.password;
      const emailTemplate = isCreateNew ? 'create-new-password.html' : 'password-reset.html';
      const resetPasswordURLBuilder = new URL(
        `${clinicianPortalUrl}/account/token-verification-before-set-or-reset-password?token=${user.resetPasswordToken}`,
      );
      let html = fs.readFileSync(`./src/assets/email-templates/${emailTemplate}`, 'utf-8');
      html = html
        .replace('#FirstName#', `${user.firstName}`)
        .replace('#LastName#', `${user.lastName}`)
        .replace('#PasswordReset.Link#', resetPasswordURLBuilder.href)
        .replace('#PasswordReset.Link#', resetPasswordURLBuilder.href)
        .replace('#UserName#', user.username);

      const emailTitle = isCreateNew ? 'Set a password' : 'Reset Password';

      await sendMail(user.email, emailTitle, html);
    } catch (ex) {
      this.logger.error(`Cannot send email: ${ex}`);
    }
  }

  async sendClinicianOnboardingEmail(user): Promise<void> {
    try {
      let html = fs.readFileSync(`./src/assets/email-templates/clinician-onboarding.html`, 'utf-8');
      html = html
        .replace('#FirstName#', `${user.firstName}`)
        .replace('#LastName#', `${user.lastName}`)
        .replace('#ClinicianPortal.Link#', clinicianPortalUrl)
        .replace('#ClinicianPortal.Link#', clinicianPortalUrl)
        .replace('#UserName#', user.username);

      const emailTitle = 'Welcome to IBD-PRAM Platform';

      await sendMail(user.email, emailTitle, html);
      this.logger.log(`Clinician onboarding email sent to: ${user.email}`);
    } catch (ex) {
      this.logger.error(`Cannot send clinician onboarding email: ${ex}`);
    }
  }

  async regenerateResetPasswordToken(token): Promise<void> {
    const user = await this.userModel
      .findOne({
        resetPasswordToken: token,
      })
      .exec();
    user.resetPasswordToken = uuidv4();
    await this.userModel.updateOne(
      { _id: user._id },
      {
        $set: {
          resetPasswordToken: user.resetPasswordToken,
        },
      },
    );
  }

  async getUserByResetPasswordToken(token: string): Promise<any> {
    return await this.userModel
      .findOne({
        resetPasswordToken: token,
      })
      .select('isFirstTimeLogin requestResetPasswordDate username')
      .exec();
  }

  async verifyResetPasswordToken(
    resetPasswordTokenDto: VerifyResetPasswordTokenDto,
  ): Promise<VerifyResetPasswordTokenRO> {
    const user = await this.userModel
      .findOne({
        resetPasswordToken: resetPasswordTokenDto.token,
      })
      .exec();
    return {
      username: user.username,
    };
  }

  async getUserByPagination(getUserDto: GetUserDto): Promise<GetUsersRO> {
    const roleFilter = UserService.buildRoleQuery(getUserDto);
    const archiveFilter = UserService.buildIsArchiveQuery(getUserDto);
    const sortBy = UserService.buildSortBy(getUserDto);
    const searchFilter = UserService.buildSearchQuery(getUserDto);
    const total = await this.userModel
      .find({
        ...roleFilter,
        ...archiveFilter,
        ...searchFilter,
      })
      .countDocuments();
    const limit = +getUserDto.limit <= 0 ? total : +getUserDto.limit;
    const page = +getUserDto.page;
    const users = await this.userModel
      .find({
        ...roleFilter,
        ...archiveFilter,
      })
      .skip((page - 1) * limit)
      .limit(limit)
      .collation({ locale: 'en' })
      .sort({ ...sortBy });
    const usersRO = this.buildGetUsersRO(users);
    return {
      limit,
      page,
      total,
      users: usersRO,
    };
  }

  async archiveUser(archiveUserDto: ArchiveUserDto, userSession): Promise<ArchiveUserRO> {
    const user = await this.userModel.findOne({
      _id: archiveUserDto.id,
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.username === userSession.username) {
      throw new BadRequestException('User cannot archive themself.');
    }
    if (user.role === UserRole.SUPER_ADMIN && userSession.role == UserRole.CLINICIAN) {
      throw new ForbiddenException('Clinician cannot archive super admin');
    }

    if (user.role === UserRole.DEMO && userSession.role === UserRole.CLINICIAN) {
      throw new ForbiddenException('Clinician cannot archive demo user');
    }

    try {
      user.isArchived = archiveUserDto.status;
      if (archiveUserDto.status) {
        user.archiveReason = archiveUserDto.archiveReason;
      }
      await user.save();
    } catch (ex) {
      const action = archiveUserDto.status ? UserStatus.ARCHIVED : UserStatus.UNARCHIVED;
      throw new BadRequestException(`We couldn't ${action} the patient. Please try again later or contact support.`);
    }
    return {
      id: user.id,
      isArchived: user.isArchived,
      username: user.username,
    };
  }

  async getUserById(id: string): Promise<GetUserRO> {
    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return UserService.buildGetUserRO(user);
  }

  async setRevealPassword(id, password): Promise<void> {
    const user = await this.userModel.findById(id);
    user.revealPassword = password;
    await user.save();
  }

  private buildGetUsersRO(users): GetUserRO[] {
    const items: GetUserRO[] = [];
    users.forEach((entry) => items.push(UserService.buildGetUserRO(entry)));
    return items;
  }

  private static buildGetUserRO(user): GetUserRO {
    if (!user) {
      return null;
    }
    return {
      id: user.id,
      username: user.username,
      designation: user.designation,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      mobileNumber: user.mobileNumber,
      role: user.role,
      site: user.site,
      isArchived: user.isArchived,
      archiveReason: user.archiveReason,
      nhsNumber: user.nhsNumber,
    };
  }

  private static buildRoleQuery(getUserDto) {
    if (!getUserDto.role) {
      return {};
    }
    const roles = getUserDto.role.split(',');
    return {
      role: {
        $in: roles,
      },
    };
  }

  private static buildIsArchiveQuery(getUserDto: GetUserDto) {
    if (getUserDto.isArchived === undefined) {
      return {};
    }
    return {
      isArchived: getUserDto.isArchived,
    };
  }

  private static buildSortBy(getUserDto: GetUserDto) {
    if (!getUserDto.sortBy || !getUserDto.sortType) {
      return {};
    }
    const sort = {};
    sort[getUserDto.sortBy] = getUserDto.sortType;
    return sort;
  }

  private static buildSearchQuery(getUserDto: GetUserDto) {
    const { searchText } = getUserDto;
    if (!searchText) {
      return {};
    }
    const searchRegex = new RegExp(searchText.replace(/[-[\]{}()*+?.,\\/^$|#\s]/g, '\\$&'));
    const searchQuery = [
      {
        firstName: {
          $regex: searchRegex,
          $options: 'i',
        },
      },
      {
        lastName: {
          $regex: searchRegex,
          $options: 'i',
        },
      },
    ];
    if (getUserDto.role.indexOf(UserRole.PATIENT.toString()) > -1) {
      return {
        $or: [
          ...searchQuery,
          {
            nhsNumber: {
              $regex: searchRegex,
              $options: 'i',
            },
          },
        ],
      };
    }
    return {
      $or: [...searchQuery],
    };
  }
}
