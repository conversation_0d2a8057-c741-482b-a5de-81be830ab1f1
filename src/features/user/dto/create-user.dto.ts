import { UserRole } from '@aos-enum/user-role.enum';
import { IsEmailAlreadyExist } from '@aos-validator/is-email-already-exist.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, Matches } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsOptional()
  @IsEmail()
  @IsEmailAlreadyExist({
    message: 'Email $value already exists. Choose another email.',
  })
  readonly email: string;

  @ApiProperty()
  @IsString()
  @Matches(/^[a-z-' ]+$/i, {
    message: 'First Name can only contain alphabetical characters',
  })
  readonly firstName: string;

  @ApiProperty()
  @IsString()
  @Matches(/^[a-z-' ]+$/i, {
    message: 'Last Name can only contain alphabetical characters',
  })
  readonly lastName: string;

  @ApiProperty()
  @IsEnum(UserRole)
  @IsNotEmpty()
  role: UserRole;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  designation: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  site: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  // @Matches(/^07[0-9]{9}$/, {
  //   message: 'This field can only contain alphanumeric characters',
  // })
  mobileNumber: string;
}
