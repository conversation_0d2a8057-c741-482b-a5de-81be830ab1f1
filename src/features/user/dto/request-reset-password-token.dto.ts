import { IsEmailNotExist } from '@aos-validator/is-email-not-exist.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsDefined, IsEmail, MaxLength } from 'class-validator';

export class RequestResetPasswordTokenDto {
  @IsDefined({
    message: 'This is a required field.',
  })
  @MaxLength(254, {
    message: 'This field should be max 254 characters long',
  })
  @IsEmail(
    {},
    {
      message: 'Please enter a valid email address.',
    },
  )
  @IsEmailNotExist({
    message: 'Email $value does not exists.',
  })
  @ApiProperty()
  readonly email: string;
}
