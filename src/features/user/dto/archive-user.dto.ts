import { MESSAGES } from '@aos-shared/constants/error-messages.constant';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsOptional, IsString, MaxLength } from 'class-validator';

export class ArchiveUserDto {
  @ApiProperty()
  @IsString()
  readonly id: string;

  @ApiProperty({
    required: false,
    maxLength: 150,
  })
  @IsOptional()
  @IsString()
  @MaxLength(150, { message: MESSAGES.ARCHIVE_USER_REASON_MAX_LENGTH })
  archiveReason: string;

  @ApiProperty()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  readonly status: boolean;
}
