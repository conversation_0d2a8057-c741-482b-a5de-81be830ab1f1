import { IsResetPasswordTokenExist } from '@aos-validator/is-reset-password-token-exist.validator';
import { IsResetPasswordTokenNotExpired } from '@aos-validator/is-reset-password-token-not-expired.validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class VerifyResetPasswordTokenDto {
  @IsNotEmpty()
  @IsResetPasswordTokenExist({
    message: 'Token $value does not exist. Please try again.',
  })
  @IsResetPasswordTokenNotExpired({
    message: 'Sorry, this link has expired.',
  })
  @ApiProperty()
  readonly token: string;
}
