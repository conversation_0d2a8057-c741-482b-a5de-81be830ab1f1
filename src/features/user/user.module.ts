import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LoggerModule } from '../../logger/logger.module';
import { FirebaseService } from '../../shared/services/firebase.service';


import { DatabaseModule } from '@aos-database/database.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { EmailService } from '../../shared/services/email.service';
import { SharedModule } from '../../shared/shared.module';
import { UserController } from './user.controller';
import { UserCronService } from './user.cron.service';
import { UserService } from './user.service';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
    LoggerModule.forRoot(),
    CacheModule.register({
      ttl: null,
    }),
  ],
  providers: [UserService, UserCronService, FirebaseService, EmailService],
  controllers: [UserController],
  exports: [PassportModule],
})
export class UserModule {}
