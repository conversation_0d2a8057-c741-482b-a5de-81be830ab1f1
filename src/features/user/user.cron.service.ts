import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';

import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { Cron } from '@nestjs/schedule';
import * as moment from 'moment';
import { deleteExpiredTokenUserCron, setPasswordTokenExpired } from '../../app.config';

@Injectable()
export class UserCronService {
  private readonly logger = new Logger(UserCronService.name);
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
  ) {}

  @Cron(deleteExpiredTokenUserCron)
  async deleteUserWhichTokenExpired(): Promise<void> {
    this.logger.log(`Start the cron job: ${deleteExpiredTokenUserCron}`);
    const expiredTime = moment.utc().subtract(setPasswordTokenExpired, 'seconds').format();
    const query = {
      requestResetPasswordDate: { $lte: expiredTime },
      password: { $exists: false },
    };
    try {
      const users = await this.userModel.deleteMany({
        ...query,
      });
      this.logger.log(`Deleted ${users.deletedCount}`);
    } catch (ex) {
      this.logger.error('cron job exec failed');
    }
  }
}
