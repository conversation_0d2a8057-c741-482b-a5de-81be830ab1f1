import { Model } from 'mongoose';
import { AosCollections, getCollectionName, getCollectionToken } from '@aos-database/database.constant';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Session,
  UseGuards,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto } from './dto';
import { UserRole } from '@aos-enum/user-role.enum';
import { CreateUserInterface } from './interface/create-user.interface';
import { RequestResetPasswordTokenDto } from './dto/request-reset-password-token.dto';
import { ResetPasswordDto } from '../auth/dto/reset-password.dto';
import { VerifyResetPasswordTokenDto } from './dto/verify-reset-password-token.dto';
import { ResetPasswordRO } from './interface/reset-password.interface';
import { VerifyResetPasswordTokenRO } from './interface/verify-reset-password-token.interface';
import { PermissionGuard } from '@aos-guard/permission.guard';
import { Permissions } from '@aos-decorator/permission.decorator';
import { GetUserDto } from './dto/get-user-dto';
import { GetUserRO, GetUsersRO } from './interface/get-user.ro';
import { ArchiveUserDto } from './dto/archive-user.dto';
import { ArchiveUserRO } from './interface/archive-user.ro';
import { User } from '@aos-decorator/user.decorator';
import { UserCronService } from './user.cron.service';
import { Cache } from 'cache-manager';
import { AuthGuard } from '@aos-shared/guards/auth.guard';

@Controller('user')
@ApiTags('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly userCronService: UserCronService,
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  @ApiOperation({ description: ' Create User... ' })
  @Post()
  @Permissions(UserRole.SUPER_ADMIN)
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionGuard)
  async createUser(@Body() createUserDto: CreateUserDto, @User() user): Promise<CreateUserInterface> {
    return await this.userService.createUser(createUserDto);
  }

  @ApiOperation({ description: ' Request reset password token ' })
  @Post('request-reset-password-token')
  async requestResetPasswordToken(@Body() requestResetPasswordToken: RequestResetPasswordTokenDto): Promise<any> {
    return await this.userService.requestResetPasswordToken(requestResetPasswordToken);
  }

  @ApiOperation({ description: ' Reset password ' })
  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<ResetPasswordRO> {
    return await this.userService.resetPassword(resetPasswordDto);
  }

  @ApiOperation({ description: ' Verify reset password token ' })
  @Get('verify-reset-password-token')
  async verifyResetPasswordToken(
    @Query() verifyResetPasswordTokenDto: VerifyResetPasswordTokenDto,
    @Session() session: Record<string, any>,
  ): Promise<VerifyResetPasswordTokenRO> {
    // NOTE: Already Verify via DTO validator
    const { token } = verifyResetPasswordTokenDto;

    // Get user to check isFirstTimeLogin status and requestResetPasswordDate
    const user = await this.userService.getUserByResetPasswordToken(token);
    if (!user) {
      throw new BadRequestException('Reset Password token expired or invalid');
    }

    // Calculate expiration based on user's isFirstTimeLogin status
    const currentDateTime = new Date().getTime();
    const requestDateTime = new Date(user.requestResetPasswordDate).getTime();

    // 48 hours (172,800 seconds) for first-time login, 4 hours (14,400 seconds) for password reset
    const expirationPeriodMs = user.isFirstTimeLogin ? 172800 * 1000 : 14400 * 1000;
    const tokenExpirationTime = requestDateTime + expirationPeriodMs;

    if (currentDateTime >= tokenExpirationTime) {
      throw new BadRequestException('Reset Password token expired or invalid');
    }

    return { username: user.username };
  }

  @ApiOperation({ description: ' Get User by query and pagination' })
  @Get()
  @Permissions(UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  public async getUsersPagination(@Query() getUserDto: GetUserDto): Promise<GetUsersRO> {
    return await this.userService.getUserByPagination(getUserDto);
  }

  @ApiOperation({ description: ' View User by Detail' })
  @Get(':id')
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  public async getUserById(@Param('id') id): Promise<GetUserRO> {
    return await this.userService.getUserById(id);
  }

  @ApiOperation({ description: ' Archive/Unarchive User' })
  @Put('archive')
  @Permissions(UserRole.SUPER_ADMIN, UserRole.CLINICIAN)
  @UseGuards(AuthGuard, PermissionGuard)
  @ApiBearerAuth()
  public async archiveUser(@Body() archiveUserDto: ArchiveUserDto, @User() userSession): Promise<ArchiveUserRO> {
    return await this.userService.archiveUser(archiveUserDto, userSession);
  }

  @ApiOperation({
    description: ' Deleted token expired user which require create new password',
  })
  @Delete('expired')
  @Permissions(UserRole.SUPER_ADMIN)
  @UseGuards(AuthGuard, PermissionGuard)
  public async deleteExpiredUsers(): Promise<void> {
    return await this.userCronService.deleteUserWhichTokenExpired();
  }
}
