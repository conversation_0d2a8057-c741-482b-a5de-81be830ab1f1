import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LoggerModule } from '../../logger/logger.module';
import { TestController } from './test.controller';


import { DatabaseModule } from '@aos-database/database.module';
import { jwtSecret, tokenExpired } from '../../app.config';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [
    DatabaseModule,
    SharedModule,
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: jwtSecret,
      signOptions: {
        expiresIn: tokenExpired,
      },
    }),
    LoggerModule.forRoot(),
  ],
  providers: [],
  controllers: [TestController],
})
export class TestModule {}
