import { Permissions } from '@aos-decorator/permission.decorator';
import { User } from '@aos-decorator/user.decorator';
import { UserRole } from '@aos-enum/user-role.enum';
import { PermissionGuard } from '@aos-guard/permission.guard';
import { AuthGuard } from '@aos-shared/guards/auth.guard';
import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { TestDetailRO, TestRO } from './interface/test.interface';
import { TestService } from './test.service';

@ApiBearerAuth()
@ApiTags('tests')
@Controller('tests')
export class TestController {
  constructor(private readonly testService: TestService) {}

  @ApiOperation({ description: ' Get all tests ' })
  @Get()
  @Permissions(UserRole.PATIENT, UserRole.DEMO)
  @UseGuards(AuthGuard, PermissionGuard)
  async findAll(@User() user): Promise<TestRO[]> {
    return this.testService.getCurrentTestResults(user);
  }

  @ApiOperation({ description: ' Get test detail ' })
  @Get(':id')
  @Permissions(UserRole.PATIENT, UserRole.DEMO)
  @UseGuards(AuthGuard, PermissionGuard)
  async findOne(@Param('id') id: string, @User() user): Promise<TestDetailRO> {
    return this.testService.getTestDetailById(id, user);
  }
}
