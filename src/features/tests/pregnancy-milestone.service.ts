import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import {
  PREGNANCY_MILESTONE_DESCRIPTIONS,
  PregnancyConstants,
  PregnancyMilestoneType,
} from '../../shared/enums/pregnancy-milestone.enum';

dayjs.extend(utc);

export interface PregnancyMilestone {
  type: PregnancyMilestoneType;
  dueDate: Date;
  description: string;
}

@Injectable()
export class PregnancyMilestoneService {
  public calculateNextTest3DueDate(
    expectedDueDate: Date,
    installationDate: Date,
    completedDate?: Date,
  ): Date | null {
    // Input validation
    if (!expectedDueDate || !installationDate) {
      throw new Error('Expected due date and installation date are required');
    }

    if (!dayjs(expectedDueDate).isValid() || !dayjs(installationDate).isValid()) {
      throw new Error('Invalid date provided');
    }

    const compareDate = completedDate ? dayjs(completedDate).utc() : dayjs().utc();

    // If EDD is in the past, return null
    if (dayjs(expectedDueDate).utc().isBefore(compareDate)) {
      return null;
    }

    const milestones = this.generatePregnancyMilestones(expectedDueDate, installationDate);

    // Find the nearest future milestone
    let nearestFutureMilestone: Date | null = null;
    let nearestDiff = Number.MAX_SAFE_INTEGER;

    for (let i = 0; i < milestones.length; i++) {
      const milestone = milestones[i];
      const milestoneDate = dayjs(milestone.dueDate).utc();

      if (milestoneDate.isAfter(compareDate)) {
        const diff = milestoneDate.diff(compareDate);
        if (diff < nearestDiff) {
          nearestDiff = diff;
          nearestFutureMilestone = milestone.dueDate;
        }
      }
    }

    return nearestFutureMilestone;
  }

  public generatePregnancyMilestones(expectedDueDate: Date, installationDate: Date): PregnancyMilestone[] {
    // Input validation
    if (!expectedDueDate || !installationDate) {
      throw new Error('Expected due date and installation date are required');
    }

    if (!dayjs(expectedDueDate).isValid() || !dayjs(installationDate).isValid()) {
      throw new Error('Invalid date provided');
    }

    const edd = dayjs(expectedDueDate).utc();
    const installation = dayjs(installationDate).utc();

    const milestones: PregnancyMilestone[] = [];

    // Always include baseline (Day 3 after installation)
    const baselineDate = installation.clone().add(PregnancyConstants.BASELINE_OFFSET_DAYS, 'days');

    // Calculate pregnancy milestones based on EDD
    const week13Date = edd.clone().subtract(PregnancyConstants.WEEK_13_OFFSET_WEEKS, 'weeks');
    const week26Date = edd.clone().subtract(PregnancyConstants.WEEK_26_OFFSET_WEEKS, 'weeks');
    const week39Date = edd.clone().subtract(PregnancyConstants.WEEK_39_OFFSET_WEEKS, 'week');

    // Only include milestones that are after the baseline date
    if (week13Date.isAfter(baselineDate)) {
      milestones.push({
        type: PregnancyMilestoneType.WEEK_13,
        dueDate: week13Date.toDate(),
        description: PREGNANCY_MILESTONE_DESCRIPTIONS[PregnancyMilestoneType.WEEK_13],
      });
    }

    if (week26Date.isAfter(baselineDate)) {
      milestones.push({
        type: PregnancyMilestoneType.WEEK_26,
        dueDate: week26Date.toDate(),
        description: PREGNANCY_MILESTONE_DESCRIPTIONS[PregnancyMilestoneType.WEEK_26],
      });
    }

    if (week39Date.isAfter(baselineDate)) {
      milestones.push({
        type: PregnancyMilestoneType.WEEK_39,
        dueDate: week39Date.toDate(),
        description: PREGNANCY_MILESTONE_DESCRIPTIONS[PregnancyMilestoneType.WEEK_39],
      });
    }

    return milestones;
  }

  public formatDueDateText(dueDate: Date | null): string {
    if (!dueDate) {
      return PregnancyConstants.DATE_FORMAT;
    }

    if (!dayjs(dueDate).isValid()) {
      return 'Invalid Date';
    }

    return dayjs(dueDate).format(PregnancyConstants.DATE_FORMAT);
  }

  public getTotalMilestonesCount(expectedDueDate: Date, installationDate: Date): number {
    try {
      const milestones = this.generatePregnancyMilestones(expectedDueDate, installationDate);
      return milestones.length;
    } catch (error) {
      return 0;
    }
  }

  public getCurrentMilestoneDescription(expectedDueDate: Date, installationDate: Date): string {
    try {
      const milestones = this.generatePregnancyMilestones(expectedDueDate, installationDate);
      const now = dayjs().utc();

      for (let i = 0; i < milestones.length; i++) {
        const milestone = milestones[i];
        const milestoneDate = dayjs(milestone.dueDate).utc();

        if (now.isBefore(milestoneDate) || now.isSame(milestoneDate, 'day')) {
          return milestone.description;
        }
      }

      return 'All pregnancy milestones completed';
    } catch (error) {
      return 'Unable to determine milestone status';
    }
  }
}
