import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { TestResultStatus } from '@aos-enum/test-result-status.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { UserRole } from '@aos-enum/user-role.enum';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import * as fs from 'fs';
import * as moment from 'moment';
import { Model } from 'mongoose';
import { TestDetailRO, TestRO } from './interface/test.interface';
import { PregnancyMilestoneService } from './pregnancy-milestone.service';
import { PregnancyConstants } from '@aos-enum/pregnancy-milestone.enum';
import { NotificationSchedulerService } from '../../shared/services/notification-scheduler.service';
import { OVERDUE_INTERVAL_DAYS } from '@aos-shared/constants/overdue';

@Injectable()
export class TestService {
  constructor(
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Test))
    private readonly testModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.Question))
    private readonly questionModel: Model<any>,
    private readonly pregnancyMilestoneService: PregnancyMilestoneService,
    private readonly notificationSchedulerService: NotificationSchedulerService,
  ) { }

  public async createNewPatientTestResults(user): Promise<void> {
    if (!user.isFirstTimeLogin) {
      return;
    }

    const tests = await this.findTestsBeforeCreating(user);

    for (const test of tests) {
      await this.createTestResultForNewPatient(user, test);
    }
  }

  private async createTestResultForNewPatient(user, test): Promise<void> {
    const now = moment().utc();

    const dueDate = await this.calculateInitialDueDate(user, test, now);

    const newTestResult = new this.testResultModel({
      patient: user,
      test,
      detail: test,
      type: test.type,
      title: test.title,
      dueDate: dueDate ? dueDate.toDate() : null, // Convert Moment to Date
    });

    await newTestResult.save();

    // Schedule notification for the new test due date
    if (dueDate) {
      await this.notificationSchedulerService.scheduleTestDueNotification(
        user,
        test.type,
        test.title,
        dueDate.toDate(),
      );
    }
  }

  private async calculateInitialDueDate(user, test, now: moment.Moment): Promise<moment.Moment | null> {
    if (test.type === TestType.GENERAL_WELLBEING && user.expectedDueDate) {
      return this.calculateInitialPregnancyTestDueDate(user, now);
    } else {
      return now.add(test.firstTimeIntervalDays, 'days');
    }
  }

  private calculateInitialPregnancyTestDueDate(user, createdAt: moment.Moment): moment.Moment | null {
    return moment().add(PregnancyConstants.BASELINE_OFFSET_DAYS, 'days');
  }

  public async createTestForNewSubtype(user): Promise<void> {
    if (!user.ibdSubtype) {
      return;
    }

    const symptomsTest = await this.findTestByType(TestType.SYMPTOMS, user.ibdSubtype);

    if (!symptomsTest) {
      return;
    }

    await this.testResultModel.deleteOne({
      patient: user._id,
      type: TestType.SYMPTOMS,
      status: TestResultStatus.NEW,
    });

    const now = moment().utc();
    const dueDate = now.toDate(); // Convert Moment to Date

    const newTestResult = new this.testResultModel({
      patient: user,
      test: symptomsTest,
      detail: symptomsTest,
      type: symptomsTest.type,
      title: symptomsTest.title,
      dueDate,
    });

    await newTestResult.save();
  }

  public async createNewTestAfterPatientFinishedByType(user, testResult): Promise<void> {
    if (await this.isCompletedTestExisted(user, testResult)) {
      return;
    }

    const testType = testResult.type;
    const effectiveIBDSubtype = this.getEffectiveIBDSubtype(user);
    const test = await this.findTestByType(testType, effectiveIBDSubtype);

    const completedDate = testResult.completedDate ? moment(testResult.completedDate).utc() : moment().utc();

    const dueDate = await this.calculateNextTestDueDate(user, testType, test, completedDate);

    const newTestResult = new this.testResultModel({
      patient: user,
      test,
      detail: test,
      type: testType,
      title: test.title,
      dueDate: dueDate ? dueDate.toDate() : null, // Convert Moment to Date
      previousCompletedDate: completedDate.toDate(), // Convert Moment to Date
    });

    await newTestResult.save();

    // Schedule notification for the updated test due date
    if (dueDate) {
      await this.notificationSchedulerService.scheduleTestDueNotification(
        user,
        testType,
        test.title,
        dueDate.toDate(),
      );
    }
  }

  private async calculateNextTestDueDate(user, testType: TestType, test, completedDate: moment.Moment): Promise<moment.Moment | null> {
    if (testType === TestType.GENERAL_WELLBEING && user.expectedDueDate) {
      return await this.calculatePregnancyMilestoneDueDate(user, completedDate);
    } else {
      return moment(completedDate).add(test.intervalDays, 'days');
    }
  }

  private async calculatePregnancyMilestoneDueDate(user, completedDate: moment.Moment): Promise<moment.Moment | null> {
    try {
      const nextMilestoneDueDate = this.pregnancyMilestoneService.calculateNextTest3DueDate(
        user.expectedDueDate,
        user.createdAt,
        completedDate.toDate(),
      );

      return nextMilestoneDueDate ? moment(nextMilestoneDueDate).utc() : null;
    } catch (error) {
      throw new Error("We couldn't update your test due dates. Please try again later.");
    }
  }

  public async createNewTestAfterCancelled(user, cancelTestResult) {
    const testType = cancelTestResult.type;
    const effectiveIBDSubtype = this.getEffectiveIBDSubtype(user);
    const test = await this.findTestByType(testType, effectiveIBDSubtype);
    const dueDate = cancelTestResult.dueDate;
    const newTestResult = new this.testResultModel({
      patient: user,
      test,
      detail: test,
      type: testType,
      title: test.title,
      dueDate,
    });
    await newTestResult.save();
  }

  public async createTestByOverdue(overdueTestResult) {
    const testId = overdueTestResult._id?.toString();
    const patientId = overdueTestResult.patient?._id?.toString() || overdueTestResult.patient?.toString();
    const currentOverdueCount = overdueTestResult.overDueCount || 0;
    const newOverdueCount = currentOverdueCount + 1;

    try {
      this.logger.log(`Creating overdue test for ${testId} (Patient: ${patientId}, new overdue count: ${newOverdueCount})`);

      // Calculate due date with overlap handling
      const dueDate = moment(overdueTestResult.dueDate).utc();

      try {
        if (await this.isTestDueDateOverlap(overdueTestResult)) {
          this.logger.log(`Due date overlap detected for test ${testId}, adding 1 day`);
          dueDate.add({ days: 1 });
        }
      } catch (overlapError) {
        this.logger.error(`Failed to check due date overlap for test ${testId}: ${overlapError.message}`, {
          testId,
          patientId,
          error: overlapError.message,
          stage: 'due_date_overlap_check'
        });
        // Continue without overlap adjustment if check fails
      }

      // Create new test result
      const newTestResult = new this.testResultModel({
        patient: overdueTestResult.patient,
        test: overdueTestResult.test,
        detail: overdueTestResult.detail,
        type: overdueTestResult.type,
        title: overdueTestResult.title,
        dueDate: dueDate.toDate(), // Convert Moment to Date
        previousCompletedDate: overdueTestResult.previousCompletedDate,
        overDueCount: newOverdueCount,
      });

      // Save with error handling
      try {
        await newTestResult.save();
        this.logger.log(`Successfully created overdue test for ${testId} (Patient: ${patientId}, overdue count: ${newOverdueCount})`);
      } catch (saveError) {
        this.logger.error(`Failed to save new overdue test for ${testId}: ${saveError.message}`, {
          testId,
          patientId,
          newOverdueCount,
          error: saveError.message,
          stack: saveError.stack,
          stage: 'new_test_save'
        });
        throw saveError; // Re-throw as this is critical
      }

    } catch (error) {
      this.logger.error(`Failed to create overdue test for ${testId} (Patient: ${patientId}): ${error.message}`, {
        testId,
        patientId,
        currentOverdueCount,
        newOverdueCount,
        error: error.message,
        stack: error.stack,
        stage: 'create_test_by_overdue'
      });
      throw error;
    }
  }

  public async createTestResults(user, cancelTestResult = null) {
    const tests = await this.findTestsBeforeCreating(user);
    const testResults = await this.getExistingTestResults(user);
    let dueDate = new Date();

    for (const test of tests) {
      const testExisted = await this.isCompletedTestExisted(test, testResults);
      if (!testExisted) {
        dueDate = await this.calculateDueDate(user, test, cancelTestResult, dueDate);
        const newTestResult = await this.createNewTestResult(user, test, dueDate);
        await this.resolveDateConflicts(newTestResult, testResults, dueDate);
        await newTestResult.save();
      }
    }
  }

  private async getExistingTestResults(user): Promise<any[]> {
    return await this.testResultModel
      .find({
        patient: user._id,
        status: {
          $nin: [TestResultStatus.COMPLETED, TestResultStatus.CANCELLED],
        },
      })
      .sort({ type: 1 });
  }

  private async calculateDueDate(user, test, cancelTestResult, currentDueDate: Date): Promise<Date> {
    if (cancelTestResult != null && cancelTestResult.type === test.type) {
      return cancelTestResult.dueDate;
    }

    const completedTestResults = await this.testResultModel.find({
      patient: user._id,
      type: test.type,
      status: TestResultStatus.COMPLETED,
    });

    const dueDate = new Date(currentDueDate);

    if (completedTestResults.length === 1) {
      this.addDaysBasedOnTestType(dueDate, test.type);
    } else {
      dueDate.setDate(dueDate.getDate() + OVERDUE_INTERVAL_DAYS);
    }

    return dueDate;
  }

  private addDaysBasedOnTestType(dueDate: Date, testType: TestType): void {
    if (testType === TestType.SYMPTOMS) {
      dueDate.setDate(dueDate.getDate() + 14);
    } else if (testType === TestType.MENTAL_WELLBEING) {
      dueDate.setDate(dueDate.getDate() + 28);
    } else if (testType === TestType.GENERAL_WELLBEING) {
      dueDate.setDate(dueDate.getDate() + 42);
    }
  }

  private async createNewTestResult(user, test, dueDate: Date): Promise<any> {
    return new this.testResultModel({
      patient: user,
      test,
      detail: test,
      type: test.type,
      title: test.title,
      dueDate,
    });
  }

  private async resolveDateConflicts(newTestResult, testResults: any[], dueDate: Date): Promise<void> {
    if (newTestResult.type === TestType.SYMPTOMS) {
      await this.resolveSymptomsDateConflicts(newTestResult, testResults, dueDate);
    } else if (newTestResult.type === TestType.MENTAL_WELLBEING) {
      await this.resolveMentalWellbeingDateConflicts(newTestResult, testResults, dueDate);
    } else if (newTestResult.type === TestType.GENERAL_WELLBEING) {
      await this.resolveGeneralWellbeingDateConflicts(newTestResult, testResults, dueDate);
    }
  }

  private async resolveSymptomsDateConflicts(newTestResult, testResults: any[], dueDate: Date): Promise<void> {
    for (const testResult of testResults) {
      if (testResult.dueDate === newTestResult.dueDate) {
        dueDate.setDate(dueDate.getDate() + 1);
        testResult.dueDate = dueDate;
        await testResult.save();
      }
    }
  }

  private async resolveMentalWellbeingDateConflicts(newTestResult, testResults: any[], dueDate: Date): Promise<void> {
    for (const testResult of testResults) {
      if (testResult.dueDate === newTestResult.dueDate) {
        if (testResult.type === TestType.SYMPTOMS) {
          testResult.dueDate = dueDate;
          newTestResult.dueDate.setDate(dueDate.getDate() + 1);
        }

        if (testResult.type === TestType.MENTAL_WELLBEING) {
          testResult.dueDate.setDate(newTestResult.dueDate.getDate() + 1);
        }

        await testResult.save();
      }
    }
  }

  private async resolveGeneralWellbeingDateConflicts(newTestResult, testResults: any[], dueDate: Date): Promise<void> {
    let sameDateWithSymptoms = false;

    for (const testResult of testResults) {
      if (testResult.dueDate === newTestResult.dueDate) {
        if (testResult.type === TestType.SYMPTOMS) {
          testResult.dueDate = dueDate;
          sameDateWithSymptoms = true;
          newTestResult.dueDate.setDate(testResult.dueDate.getDate() + 1);
        }

        if (testResult.type === TestType.GENERAL_WELLBEING) {
          testResult.dueDate.setDate(sameDateWithSymptoms ? dueDate.getDate() + 1 : dueDate.getDate());
          newTestResult.dueDate.setDate(testResult.dueDate.getDate() + 1);
        }

        await testResult.save();
      }
    }
  }

  public async getCurrentTestResults(user): Promise<TestRO[]> {
    // Return virtual test results for demo accounts
    if (user.role === UserRole.DEMO) {
      return this.getDemoTestResults(user);
    }

    const effectiveIBDSubtype = await this.validateAndGetIBDSubtype(user);
    const compatibleTestIds = await this.getCompatibleTestIds(effectiveIBDSubtype);
    const testResults = await this.fetchUserTestResults(user, compatibleTestIds);

    await this.resetUserBadge(user);

    return this.buildTestResultsResponse(testResults, user);
  }

  private async validateAndGetIBDSubtype(user): Promise<any> {
    const effectiveIBDSubtype = user.sessionIBDSubtype || user.ibdSubtype;

    if (!effectiveIBDSubtype) {
      throw new Error("We couldn't find your condition type. Please contact your clinician to update this.");
    }

    return effectiveIBDSubtype;
  }

  private async fetchUserTestResults(user, compatibleTestIds: string[]): Promise<any[]> {
    return await this.testResultModel
      .find({
        patient: user._id,
        status: TestResultStatus.NEW,
        test: { $in: compatibleTestIds },
      })
      .sort({ type: 1 });
  }

  private async resetUserBadge(user): Promise<void> {
    await this.userModel.findOneAndUpdate(
      { _id: user._id },
      { $set: { badge: 0 } },
      { new: false },
    );
  }

  private buildTestResultsResponse(testResults: any[], user): TestRO[] {
    const tests = [];

    testResults.forEach((testResult) => {
      const dueDateFormatted = this.calculateDueDateFormatted(testResult);

      tests.push({
        id: testResult.id,
        title: testResult.title,
        status: testResult.status,
        dueDate: dueDateFormatted,
      });
    });

    return tests;
  }

  private calculateDueDateFormatted(testResult): number | null {
    let dueDateFormatted: number | null = 0;
    if (testResult.dueDate === null) {
      dueDateFormatted = null;
    } else {
      dueDateFormatted = Date.parse(testResult.dueDate) / 1000;
    }

    return dueDateFormatted;
  }

  public async getTestDetailById(id: string, user = null): Promise<TestDetailRO> {
    // Return virtual test details for demo accounts
    if (user && user.role === UserRole.DEMO) {
      return this.getDemoTestDetail(id, user);
    }

    const query = await this.buildTestDetailQuery(id, user);
    const testResult = await this.testResultModel.findOne(query);

    if (!testResult) {
      throw new NotFoundException(`Test is not found.`);
    }

    const testResultDetail = await this.processTestInstructions(testResult.detail);

    return {
      id: testResult.id,
      title: testResult.title,
      status: testResult.status,
      detail: testResultDetail,
      dueDate: Date.parse(testResult.dueDate) / 1000,
    };
  }

  private async buildTestDetailQuery(id: string, user): Promise<any> {
    const query: any = {
      _id: id,
      status: {
        $nin: [TestResultStatus.COMPLETED, TestResultStatus.CANCELLED],
      },
    };

    if (user && user.role === UserRole.PATIENT) {
      const effectiveIBDSubtype = await this.validateAndGetIBDSubtype(user);
      const compatibleTestIds = await this.getCompatibleTestIds(effectiveIBDSubtype);
      query.test = { $in: compatibleTestIds };
      query.patient = user._id;
    }

    return query;
  }

  private async processTestInstructions(testResultDetail): Promise<any> {
    if (testResultDetail.instructions !== undefined) {
      const instructions = [];

      for (const instruction of testResultDetail.instructions) {
        instructions.push({
          id: instruction.id,
          title: instruction.title,
          image: fs.readFileSync(instruction.image).toString('base64'),
          content: instruction.content,
        });
      }

      testResultDetail.instructions = instructions;
    }

    return testResultDetail;
  }

  private async findTestsBeforeCreating(user) {
    if (!user.ibdSubtype) {
      throw new Error("We couldn't find your condition type. Please contact your clinician to update this.");
    }

    const symptomsQuery = {
      type: TestType.SYMPTOMS,
      ibdSubtype: user.ibdSubtype,
    };

    const otherTestsQuery = {
      type: { $in: [TestType.MENTAL_WELLBEING, TestType.GENERAL_WELLBEING] },
    };

    const query = {
      $or: [symptomsQuery, otherTestsQuery],
    };

    return this.testModel
      .find(query)
      .populate({
        path: 'questions',
        model: 'Question',
        select: '-__v',
        populate: [
          {
            path: 'answers.questions',
            model: 'Question',
            select: '-__v',
            populate: {
              path: 'answers.questions',
              model: 'Question',
              select: '-__v',
            },
          },
          {
            path: 'questions',
            model: 'Question',
            select: '-__v',
            populate: {
              path: 'answers.questions',
              model: 'Question',
              select: '-__v',
              populate: {
                path: 'answers.questions',
                model: 'Question',
                select: '-__v',
              },
            },
          },
        ],
      })
      .select('-__v');
  }

  private async getCompatibleTestIds(ibdSubtype: IBDSubtype): Promise<string[]> {
    const symptomsQuery = {
      type: TestType.SYMPTOMS,
      ibdSubtype,
    };

    const otherTestsQuery = {
      type: { $in: [TestType.MENTAL_WELLBEING, TestType.GENERAL_WELLBEING] },
    };

    const query = {
      $or: [symptomsQuery, otherTestsQuery],
    };

    const compatibleTests = await this.testModel.find(query).select('_id');
    return compatibleTests.map((test) => test._id.toString());
  }

  private getEffectiveIBDSubtype(user): IBDSubtype {
    return user.sessionIBDSubtype || user.ibdSubtype;
  }

  async findTestByType(type: TestType, ibdSubtype: IBDSubtype = null) {
    const query: any = { type };

    if (type === TestType.SYMPTOMS && ibdSubtype) {
      query.ibdSubtype = ibdSubtype;
    }

    return await this.testModel
      .findOne(query)
      .populate({
        path: 'questions',
        model: 'Question',
        select: '-__v',
        populate: [
          {
            path: 'answers.questions',
            model: 'Question',
            select: '-__v',
            populate: {
              path: 'answers.questions',
              model: 'Question',
              select: '-__v',
            },
          },
          {
            path: 'questions',
            model: 'Question',
            select: '-__v',
            populate: {
              path: 'answers.questions',
              model: 'Question',
              select: '-__v',
              populate: {
                path: 'answers.questions',
                model: 'Question',
                select: '-__v',
              },
            },
          },
        ],
      })
      .select('-__v');
  }

  async isCompletedTestExisted(user: any, testResults: any): Promise<boolean> {
    const exists = await this.testResultModel.exists({
      patient: user._id,
      status: {
        $nin: [TestResultStatus.COMPLETED, TestResultStatus.CANCELLED, TestResultStatus.OVERDUE],
      },
      test: testResults.test._id,
    });
    return !!exists;
  }

  async isTestDueDateOverlap(testResult: any): Promise<boolean> {
    const exists = await this.testResultModel.exists({
      patient: testResult.patient,
      dueDate: testResult.dueDate,
      status: {
        $nin: [TestResultStatus.COMPLETED, TestResultStatus.CANCELLED, TestResultStatus.OVERDUE],
      },
      type: { $nin: [testResult.type] },
    });
    return !!exists;
  }

  private async getDemoTestResults(user): Promise<TestRO[]> {
    // Virtual test results for demo accounts based on IBD subtype
    const effectiveIBDSubtype = user.sessionIBDSubtype || user.ibdSubtype;
    const now = moment().unix();

    const tests = [];

    if (effectiveIBDSubtype === IBDSubtype.CROHNS_DISEASE) {
      tests.push({
        id: 'demo-hbi-test',
        title: 'HBI Assessment (Demo)',
        status: TestResultStatus.NEW,
        dueDate: now,
      });
    } else if (effectiveIBDSubtype === IBDSubtype.ULCERATIVE_COLITIS) {
      tests.push({
        id: 'demo-sccai-test',
        title: 'SCCAI Assessment (Demo)',
        status: TestResultStatus.NEW,
        dueDate: now,
      });
    }

    // Add common tests
    tests.push(
      {
        id: 'demo-mental-wellbeing-test',
        title: 'Mental Wellbeing Assessment (Demo)',
        status: TestResultStatus.NEW,
        dueDate: now,
      },
      {
        id: 'demo-general-wellbeing-test',
        title: 'General Wellbeing Assessment (Demo)',
        status: TestResultStatus.NEW,
        dueDate: now,
      },
    );

    return tests;
  }

  private async getDemoTestDetail(id: string, user): Promise<TestDetailRO> {
    const effectiveIBDSubtype = user.sessionIBDSubtype || user.ibdSubtype;
    const now = moment().unix();

    const { testType, testTitle } = this.getDemoTestTypeAndTitle(id);
    const templateTest = await this.findTestByType(testType, effectiveIBDSubtype);

    if (!templateTest) {
      throw new NotFoundException(`Demo test template not found.`);
    }

    return {
      id: id as any,
      title: testTitle,
      status: TestResultStatus.NEW,
      detail: templateTest,
      dueDate: now,
    };
  }

  private getDemoTestTypeAndTitle(id: string): { testType: TestType; testTitle: string } {
    if (id === 'demo-hbi-test') {
      return {
        testType: TestType.SYMPTOMS,
        testTitle: 'HBI Assessment (Demo)',
      };
    } else if (id === 'demo-sccai-test') {
      return {
        testType: TestType.SYMPTOMS,
        testTitle: 'SCCAI Assessment (Demo)',
      };
    } else if (id === 'demo-mental-wellbeing-test') {
      return {
        testType: TestType.MENTAL_WELLBEING,
        testTitle: 'Mental Wellbeing Assessment (Demo)',
      };
    } else if (id === 'demo-general-wellbeing-test') {
      return {
        testType: TestType.GENERAL_WELLBEING,
        testTitle: 'General Wellbeing Assessment (Demo)',
      };
    } else {
      throw new NotFoundException(`Demo test not found.`);
    }
  }
}
