# Database Configuration
DB_CONN=********************************:port/db?authSource=admin

# Application Configuration
BASE_URL=http://localhost:3000
PORT=3000
NODE_ENV=dev
API_VERSION=v1

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://fe-aos-dev.b13devops.com

# JWT & Security Configuration
JWT_SECRET=secretKey
JWT_REFRESH_SECRET=refreshSecretKey
SESSION_SECRET=sessionSecretKey

# Token Expiration (in seconds)
TOKEN_EXPIRED=36500
REFRESH_TOKEN_EXPIRED=36500
SET_PASSWORD_TOKEN_EXPIRED=14400

# Application URLs
CLINICIAN_PORTAL_URL=https://fe-aos-dev.b13devops.com
URL_RESET_PASSWORD=https://fe-aos-dev.b13devops.com/account/token-verification-before-set-or-reset-password

# Thresholds
TIMED_UP_PREDEFINED_THRESHOLD=19
HEALTH_STATUS_PREDEFINED_THRESHOLD=10

# File Paths
FILE_PATH_REPORT=./files/report

# Security Configuration
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=15
PASSWORD_MIN_LENGTH=8
GLOBAL_RATE_LIMIT=100
AUTH_RATE_LIMIT=5

# Seed Data Configuration (Development/Testing Only)
# Leave empty to generate random passwords
SEED_CLINICIAN_PASSWORD=
SEED_PATIENT_PASSWORD=

# Email Configuration - SendGrid
SENDGRID_API_KEY=
SENDGRID_SENDER=
SENDGRID_SENDER_NAME=IBD-PRAM

# Email Configuration - SMTP
SMTP_EMAIL=
SMTP_URL=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# Firebase Configuration - Secure Environment Variables
# Copy the entire JSON content from Firebase Console service account key
FIREBASE_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"your-project-id","private_key_id":"your-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com"}'
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/

# Alternative: Base64 encoded JSON (useful for CI/CD environments)
# FIREBASE_SERVICE_ACCOUNT_JSON=eyJ0eXBlIjoic2VydmljZV9hY2NvdW50IiwicHJvamVjdF9pZCI6InlvdXItcHJvamVjdC1pZCIsLi4ufQ==

# Deep Links
DEEP_LINK_APP_STORE=https://www.apple.com/vn/app-store/
DEEP_LINK_GOOGLE_STORE=https://store.google.com/regionpicker
