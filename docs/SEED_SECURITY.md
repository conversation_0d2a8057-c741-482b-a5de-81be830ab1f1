# Seed Data Security Implementation

## Overview

This document describes the security improvements implemented to address SonarQube warnings about hardcoded passwords in the seed service while maintaining functionality for development and testing environments.

## Security Issues Addressed

### Before (Security Issues)
- Hardcoded passwords `'clinician123'` and `'patient123'` in seed service
- Passwords displayed in console logs during seeding
- No environment-based security controls
- SonarQube warnings: "Review this potentially hard-coded password"

### After (Secure Implementation)
- Environment-aware password generation
- Secure random passwords in production/UAT
- Optional environment variables for development convenience
- No hardcoded passwords in source code
- Secure logging practices

## Implementation Details

### 1. SeedConfigService

A new service (`src/features/seed/seed-config.service.ts`) provides secure password management:

```typescript
// Environment-aware password generation
getSeedPassword(accountType: 'clinician' | 'patient'): string

// Security configuration
getSeedPasswordConfig(): SeedPasswordConfig

// Environment security checks
isSecureEnvironment(): boolean
```

### 2. Environment-Based Configuration

#### Production/UAT Environments
- **Always** generates random passwords using `StringHelper.generatePassword()`
- Ignores any environment variables for security
- Logs security information

#### Development/Test Environments
- Checks for optional environment variables:
  - `SEED_CLINICIAN_PASSWORD`
  - `SEED_PATIENT_PASSWORD`
- Falls back to random passwords if not set
- Provides warnings about predictable passwords

### 3. Environment Variables

Add to your `.env` file for development convenience (optional):

```bash
# Seed Data Configuration (Development/Testing Only)
# Leave empty to generate random passwords
SEED_CLINICIAN_PASSWORD=your_dev_password
SEED_PATIENT_PASSWORD=your_dev_password
```

**Important**: These variables are ignored in production and UAT environments.

## Security Benefits

### 1. Eliminates SonarQube Warnings
- No hardcoded passwords in source code
- Passes static security analysis
- Follows security best practices

### 2. Environment-Appropriate Security
- **Production/UAT**: Always secure random passwords
- **Development**: Optional predictable passwords for convenience
- **Default**: Secure random passwords everywhere

### 3. Secure Logging
- Passwords logged during creation (not hardcoded in logs)
- Security configuration information logged
- No sensitive data in source code

### 4. Maintains Functionality
- Demo accounts continue using secure random passwords
- Admin accounts continue using random passwords
- CLI commands work unchanged
- Development workflow preserved

## Password Discovery Mechanisms

### 1. Real-time Console Logging
Passwords are logged during account creation:
```bash
npm run seed
🌱 Starting data seeding...
Created clinician: clinician1 with password: ABCD1234
Created clinician: clinician2 with password: EFGH5678
Created patient: patient_crohns with password: IJKL9012
```

### 2. Temporary Credentials Files (Development Only)
In non-production environments, credentials are saved to temporary files:
```bash
# After seeding, view saved credentials
npm run list-seed-credentials

# Clean up credentials files when done
npm run clean:seed-credentials
```

### 3. Demo Account CLI Command
For demo accounts with persistent passwords:
```bash
npm run cli list-demo
🎭 Demo Accounts:
  - demoHBI1: PQRS7890 (CROHNS_DISEASE)
  - demoSCCAI1: TUVW1234 (ULCERATIVE_COLITIS)
```

### 4. Database Verification
Check user existence and basic info:
```bash
npm run verify:users
```

## Usage Examples

### Production Deployment
```bash
NODE_ENV=production npm run seed
# Always generates secure random passwords
# Logs: "Using random passwords for seed data (production environment)"
# No credentials files created for security
```

### Development with Random Passwords
```bash
NODE_ENV=development npm run seed
# Generates random passwords (secure default)
# Creates temporary credentials file
# Logs: "Using random passwords for seed data (no environment passwords configured)"
```

### Development with Predictable Passwords
```bash
# Set in .env file:
SEED_CLINICIAN_PASSWORD=dev123
SEED_PATIENT_PASSWORD=test123

NODE_ENV=development npm run seed
# Uses environment passwords for convenience
# Creates temporary credentials file
# Logs: "Using environment-configured passwords for seed data (development only)"
```

## Security Considerations

### 1. Environment Separation
- Production environments ignore development convenience features
- UAT environments use production-level security
- Clear separation between secure and convenient modes

### 2. Default Security
- Secure by default (random passwords)
- Opt-in to predictable passwords in development
- No security degradation without explicit configuration

### 3. Audit Trail
- All password generation logged with context
- Security configuration clearly indicated
- Environment-specific behavior documented in logs

## Migration Guide

### For Existing Deployments
1. **No action required** - existing functionality preserved
2. **Production**: Automatically uses secure random passwords
3. **Development**: Continues with random passwords unless configured

### For Development Teams
1. **Optional**: Add environment variables to `.env` for convenience
2. **Recommended**: Use `npm run cli list-demo` to see generated passwords
3. **Testing**: Verify seed accounts work with new password generation

## Compliance

This implementation addresses:
- **SonarQube**: Eliminates hardcoded password warnings
- **Security Best Practices**: No secrets in source code
- **Environment Security**: Production-appropriate controls
- **Development Productivity**: Maintains workflow convenience

## Available Commands

### Seeding Commands
```bash
npm run seed                    # Seed all data (users + tests)
npm run cli seed               # CLI version with options
npm run cli seed --users       # Seed only users
npm run cli seed --demo        # Seed only demo accounts
```

### Password Discovery Commands
```bash
npm run cli list-demo                # List demo account passwords
npm run list-seed-credentials        # List saved seed credentials
npm run clean:seed-credentials       # Clean up credentials files
npm run verify:users                 # Verify users in database
```

### Security Testing
```bash
npm run test:seed-security           # Test security implementation
```

## Related Files

- `src/features/seed/seed-config.service.ts` - Security configuration service
- `src/features/seed/seed.service.ts` - Updated seed service
- `src/features/seed/seed.module.ts` - Module configuration
- `src/commands/list-seed-credentials.command.ts` - Credentials listing CLI
- `src/commands/clean-seed-credentials.command.ts` - Credentials cleanup CLI
- `.env.example` - Environment variable documentation
- `docs/SEED_SECURITY.md` - This documentation

## Testing

Verify the implementation:

```bash
# Test with different environments
NODE_ENV=production npm run seed
NODE_ENV=development npm run seed
NODE_ENV=test npm run seed

# Test password discovery mechanisms
npm run list-seed-credentials
npm run cli list-demo
npm run verify:users

# Test security configuration
npm run test:seed-security
```
