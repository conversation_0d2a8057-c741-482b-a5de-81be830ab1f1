# Development Environment Configuration for IBD-PRAM Backend
# Copy this file to .env.development and update with your development Firebase project details

# Database Configuration
DB_CONN=********************************:port/db?authSource=admin

# Application Configuration
BASE_URL=http://localhost:3003
PORT=3003
NODE_ENV=development
API_VERSION=v1

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://fe-aos-dev.b13devops.com

# JWT & Security Configuration
JWT_SECRET=dev-secret-key
JWT_REFRESH_SECRET=dev-refresh-secret-key
SESSION_SECRET=dev-session-secret-key

# Token Expiration (in seconds)
TOKEN_EXPIRED=36500
REFRESH_TOKEN_EXPIRED=36500
SET_PASSWORD_TOKEN_EXPIRED=14400

# Application URLs
CLINICIAN_PORTAL_URL=https://fe-aos-dev.b13devops.com
URL_RESET_PASSWORD=https://fe-aos-dev.b13devops.com/account/token-verification-before-set-or-reset-password

# Firebase Configuration - DEVELOPMENT
# Copy the entire JSON content from Firebase Console service account key
FIREBASE_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"ibd-pram-development","private_key_id":"your-dev-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_DEV_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-dev-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40ibd-pram-development.iam.gserviceaccount.com"}'
FIREBASE_DATABASE_URL=https://ibd-pram-development-default-rtdb.firebaseio.com/

# Email Configuration - SMTP (Development)
SMTP_EMAIL=*******
SMTP_URL=smtp.gmail.com
SMTP_PORT=465
SMTP_USER=*******
SMTP_PASS=your-app-password

# Deep Links
DEEP_LINK_APP_STORE=https://www.apple.com/vn/app-store/
DEEP_LINK_GOOGLE_STORE=https://store.google.com/regionpicker
