# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Upload files
/files

/config/local.json
.cursor*
.specstory*

# Security and Environment Files
development.env
production.env
staging.env
.env
.env.local
.env.production
.env.staging
*.env

# Security Keys and Certificates
*.pem
*.key
*.cert
*.crt
*.p12
*.jks

# Secrets and Configuration
/config/secrets/
secrets.json
private-key.json

# Firebase Service Account Keys
/storage/firebase/
*firebase-adminsdk*.json
*service-account*.json

# Backup files that might contain sensitive data
*.bak
*.backup
*.old
