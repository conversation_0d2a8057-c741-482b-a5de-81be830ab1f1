db.questions.updateOne({
    "text": "Have you had any difficulty breathing today?",
    "code": "Q1",
},{ $set: {
    "text": "Have you had any difficulty breathing today (or since last using the app)?"
   }
});


db.questions.updateOne({
    "text": "Have you experienced an abnormal heartbeat or palpitations?",
    "code": "Q2",
},{ $set: {
    "text": "Have you experienced an abnormal heartbeat of palpitations today (or since last using the app)?"
   }
});

db.questions.updateOne({
        "text": "Have you experienced fatigue (tiredness) today?",
        "code": "Q3",
},{ $set: {
    "text": "Have you experienced any fatigue (tiredness) today (or since last using the app)?"
   }
});

db.questions.updateOne({
        "text": "Have you felt dizzy or light-headed today (or since last using the app)?",
        "code": "Q4",
},{ $set: {
    "text": "Have you felt dizzy or lightheaded today (or since last using the app)?"
   }
});

db.questions.updateOne({
        "text": "Are your legs or ankles swollen more than usual?",
        "code": "Q5",
},{ $set: {
    "text": "Are your legs or ankles more swollen than usual?"
   }
});

db.questions.updateOne({
        "text": "Doing gardening,housework or carrying groceries",
        "code": "Q1D",
},{ $set: {
    "text": "Doing gardening, housework or carrying groceries"
   }
});

db.tests.updateOne({
    "title": "Timed Up and Go",
}, {
    $set: {
    "disclaimer": "<p>Timed Up and Go is a simple test used to assess changes in a person's mobility over a period of time. The test involves measuring the time it takes a person to rise from a chair, walk a distance of three meters, turn around, walk back to the chair and sit down.</p> <p><b>Do not undertake the test if you feel unwell.</b></p>",
    }
});

db.questions.updateOne({
    "text": "Please select the statement that best describes your symptom today:",
    "code": "Q7B",
}, {
    $set: {
    "answers.0.text": "Chest pain/discomfort only with strenuous exertion (presence of angina during a strenuous, rapid or prolonged ordinary activity such as walking or climbing the stairs)"
  }
});

db.getCollection("patient-resources").remove({ "title": "British Cardiac Patients Association" }, { "justOne": true })

# 28/09/2021
db.questions.updateOne({
        "text": "How well do you understand what things you are able to do to keep your <b>aortic stenosis</b> symptoms from getting worse (for example, regularly weighing yourself, eating a low salt diet etc.)?",
        "code": "Q5",
        "type": "MULTIPLE_CHOICE_TYPE",
        "code": "Q11",
        "order": 16,
},{ $set: {
    "text": "How well do you understand what things you are able to do to keep your <b>aortic stenosis</b> symptoms from getting worse?"
   }
});

db.getCollection("patient-resources").remove({ "title": "Patient Information Developed at RTW" }, { "justOne": true })